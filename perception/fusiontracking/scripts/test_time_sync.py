#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间同步优化测试脚本
用于验证车路融合时间同步功能
"""

import rospy
import time
import numpy as np
import matplotlib.pyplot as plt
from common_msgs.msg import sensorobjects, obupants
import yaml
import os

class TimeSyncTester:
    def __init__(self):
        rospy.init_node('time_sync_tester', anonymous=True)
        
        # 订阅话题
        self.lidar_sub = rospy.Subscriber('/lidar/objects', sensorobjects, self.lidar_callback)
        self.obu_sub = rospy.Subscriber('/obu/objects', obupants, self.obu_callback)
        
        # 数据存储
        self.lidar_timestamps = []
        self.obu_timestamps = []
        self.time_gaps = []
        self.sync_quality_scores = []
        
        # 配置参数
        self.config_path = os.path.join(os.path.dirname(__file__), '../config/time_sync_config.yaml')
        self.load_config()
        
        # 统计信息
        self.start_time = time.time()
        self.packet_count = 0
        self.outlier_count = 0
        
        rospy.loginfo("时间同步测试器已启动")
        
    def load_config(self):
        """加载时间同步配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.sync_config = config.get('time_sync', {})
                rospy.loginfo(f"已加载时间同步配置: {self.config_path}")
        except Exception as e:
            rospy.logwarn(f"无法加载配置文件: {e}, 使用默认配置")
            self.sync_config = {
                'base_offset_ms': 360.0,
                'max_time_gap_ms': 400.0,
                'outlier_threshold': 3.0
            }
    
    def lidar_callback(self, msg):
        """处理车端lidar数据"""
        self.lidar_timestamps.append(msg.timestamp)
        self.packet_count += 1
        
        # 保持数据长度
        if len(self.lidar_timestamps) > 1000:
            self.lidar_timestamps.pop(0)
    
    def obu_callback(self, msg):
        """处理路端OBU数据"""
        self.obu_timestamps.append(msg.timestamp)
        
        # 计算时间差
        if self.lidar_timestamps:
            # 找到最近的lidar时间戳
            lidar_time = self.lidar_timestamps[-1]
            obu_time = msg.timestamp
            time_gap = lidar_time - obu_time
            
            self.time_gaps.append(time_gap)
            
            # 检查是否为异常值
            if len(self.time_gaps) > 10:
                mean_gap = np.mean(self.time_gaps[-10:])
                std_gap = np.std(self.time_gaps[-10:])
                if abs(time_gap - mean_gap) > self.sync_config.get('outlier_threshold', 3.0) * std_gap:
                    self.outlier_count += 1
                    rospy.logwarn(f"检测到时间差异常值: {time_gap:.1f}ms")
            
            # 计算同步质量评分
            quality_score = self.calculate_sync_quality()
            self.sync_quality_scores.append(quality_score)
            
            # 定期输出统计信息
            if len(self.time_gaps) % 50 == 0:
                self.print_statistics()
        
        # 保持数据长度
        if len(self.obu_timestamps) > 1000:
            self.obu_timestamps.pop(0)
        if len(self.time_gaps) > 1000:
            self.time_gaps.pop(0)
        if len(self.sync_quality_scores) > 1000:
            self.sync_quality_scores.pop(0)
    
    def calculate_sync_quality(self):
        """计算同步质量评分"""
        if len(self.time_gaps) < 10:
            return 0.0
        
        recent_gaps = self.time_gaps[-10:]
        std_dev = np.std(recent_gaps)
        max_std = self.sync_config.get('max_std_deviation_ms', 50.0)
        
        # 基于标准差的质量评分
        std_score = max(0.0, 1.0 - std_dev / max_std)
        
        # 基于异常值比例的质量评分
        outlier_ratio = self.outlier_count / max(1, len(self.time_gaps))
        outlier_score = max(0.0, 1.0 - outlier_ratio * 5.0)
        
        # 综合评分
        return (std_score * 0.7 + outlier_score * 0.3)
    
    def print_statistics(self):
        """打印统计信息"""
        if not self.time_gaps:
            return
        
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        mean_gap = np.mean(self.time_gaps)
        std_gap = np.std(self.time_gaps)
        min_gap = np.min(self.time_gaps)
        max_gap = np.max(self.time_gaps)
        
        current_quality = self.sync_quality_scores[-1] if self.sync_quality_scores else 0.0
        
        rospy.loginfo("=" * 60)
        rospy.loginfo("时间同步统计信息")
        rospy.loginfo(f"运行时间: {elapsed_time:.1f}s")
        rospy.loginfo(f"数据包总数: {self.packet_count}")
        rospy.loginfo(f"时间差统计 (ms):")
        rospy.loginfo(f"  平均值: {mean_gap:.1f}")
        rospy.loginfo(f"  标准差: {std_gap:.1f}")
        rospy.loginfo(f"  最小值: {min_gap:.1f}")
        rospy.loginfo(f"  最大值: {max_gap:.1f}")
        rospy.loginfo(f"异常值数量: {self.outlier_count}")
        rospy.loginfo(f"异常值比例: {self.outlier_count/max(1, len(self.time_gaps))*100:.1f}%")
        rospy.loginfo(f"当前同步质量评分: {current_quality:.2f}")
        rospy.loginfo("=" * 60)
    
    def plot_results(self):
        """绘制结果图表"""
        if len(self.time_gaps) < 10:
            rospy.logwarn("数据不足，无法绘制图表")
            return
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # 时间差变化图
        ax1.plot(self.time_gaps, 'b-', alpha=0.7, label='时间差')
        ax1.axhline(y=np.mean(self.time_gaps), color='r', linestyle='--', label=f'平均值: {np.mean(self.time_gaps):.1f}ms')
        ax1.set_ylabel('时间差 (ms)')
        ax1.set_title('车端-路端时间差变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 同步质量评分图
        if self.sync_quality_scores:
            ax2.plot(self.sync_quality_scores, 'g-', alpha=0.7, label='同步质量评分')
            ax2.axhline(y=0.7, color='r', linestyle='--', label='良好阈值: 0.7')
            ax2.set_ylabel('质量评分')
            ax2.set_xlabel('数据包序号')
            ax2.set_title('时间同步质量评分变化')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('/tmp/time_sync_analysis.png', dpi=300, bbox_inches='tight')
        rospy.loginfo("分析图表已保存到: /tmp/time_sync_analysis.png")
        
        # 如果在图形环境中，显示图表
        try:
            plt.show()
        except:
            rospy.loginfo("无图形环境，跳过图表显示")
    
    def save_results(self):
        """保存测试结果"""
        results = {
            'test_duration': time.time() - self.start_time,
            'total_packets': self.packet_count,
            'outlier_count': self.outlier_count,
            'time_gaps': self.time_gaps,
            'sync_quality_scores': self.sync_quality_scores,
            'statistics': {
                'mean_gap': float(np.mean(self.time_gaps)) if self.time_gaps else 0.0,
                'std_gap': float(np.std(self.time_gaps)) if self.time_gaps else 0.0,
                'min_gap': float(np.min(self.time_gaps)) if self.time_gaps else 0.0,
                'max_gap': float(np.max(self.time_gaps)) if self.time_gaps else 0.0,
                'outlier_ratio': self.outlier_count / max(1, len(self.time_gaps)),
                'avg_quality_score': float(np.mean(self.sync_quality_scores)) if self.sync_quality_scores else 0.0
            }
        }
        
        result_file = '/tmp/time_sync_test_results.yaml'
        with open(result_file, 'w', encoding='utf-8') as f:
            yaml.dump(results, f, default_flow_style=False, allow_unicode=True)
        
        rospy.loginfo(f"测试结果已保存到: {result_file}")
    
    def run(self):
        """运行测试"""
        rospy.loginfo("开始时间同步测试，按Ctrl+C停止...")
        
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("测试停止，正在保存结果...")
            self.print_statistics()
            self.save_results()
            self.plot_results()

if __name__ == '__main__':
    try:
        tester = TimeSyncTester()
        tester.run()
    except rospy.ROSInterruptException:
        pass
