Panels:
  - Class: rviz/Displays
    Help Height: 70
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /detection1
        - /fusion1
        - /tracking1
        - /v2i1
        - /fusionTrackedObjects1
        - /interaction1
        - /interaction1/车身1
        - /interaction1/地图1/Marker1
      Splitter Ratio: 0.586357057094574
    Tree Height: 371
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Name: Time
    SyncMode: 0
    SyncSource: lidarpointsrviz
  - Class: rviz/Displays
    Help Height: 75
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /detection1
        - /fusion1
        - /tracking1
      Splitter Ratio: 0.6166666746139526
    Tree Height: 632
  - Class: rviz/Selection
    Name: Selection
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 10
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 200
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 1
      Class: rviz/Axes
      Enabled: true
      Length: 1
      Name: Axes
      Radius: 0.5
      Reference Frame: <Fixed Frame>
      Show Trail: false
      Value: true
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /lidarobjectsrviz
          Name: lidarobjectsrviz
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 0.5
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: lidarpointsrviz
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 1
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /lidarpointsrviz
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: lidarclusterrviz
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 10
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /lidarclusterrviz
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /image_detect_3dresults_rviz
          Name: image_detect_3dresults_rviz
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/Image
          Enabled: false
          Image Topic: /usbcamera0
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: usbcamera0
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /camera_image
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: wj_camera_0
          Normalize Range: true
          Queue Size: 2
          Transport Hint: raw
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /wj_camera_3
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: wj_camera_3
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /image_detect_results
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: image_detect_results
          Normalize Range: true
          Queue Size: 2
          Transport Hint: compressed
          Unreliable: false
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /GPS/GPS_objectsBBX
          Name: GPS_objectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: detection
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/lidarObjectBBX
          Name: lidarObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/radarObjectBBX
          Name: radarObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: sensorradarCloud
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 10
          Size (m): 0.5
          Style: Points
          Topic: /objectfusion/sensorradarCloud
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/fusionObjectBBX
          Name: fusionObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objectfusion/objectfusionMarkerArray
          Name: /objectfusionMarkerArray
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: fusion
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/trackingObjectBBX
          Name: trk_bounding_boxes
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objectTrack/trk_trajectory_points
          Name: trk_trajectory_points
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/cloudObjectsBBX
          Name: cloudObjectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /fusiontracking/v2iFusionObjectBBX
          Name: v2iFusionObjectBBX
          Namespaces:
            ID: true
            box: true
            points_and_lines: true
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /CAMERA/CAMERA_objectsBBX
          Name: CAMERA_objectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /OBU/OBU_objectsBBX
          Name: OBU_objectsBBX
          Namespaces:
            box: true
            points_and_lines: true
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusiontracking/matchedRadarObjectsBBX
          Name: matchedRadarObjectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: matchedRadarObjectsPC
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 10
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /fusion/matchedRadarObjectsPC
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: track_clusterPointcloud
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 10
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /objectTrack/track_clusterPointcloud
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
      Enabled: true
      Name: tracking
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/trackingObjectBBX
          Name: trackingObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/obuObjectBBX
          Name: obuObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /v2ifusion/fusionObjectBBX
          Name: fusionObjectBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: v2i
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /fusionTrackedObjectsBBX
          Name: fusionTrackedObjectsBBX
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: false
      Name: fusionTrackedObjects
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objects
          Name: objects
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/Marker
          Enabled: true
          Marker Topic: /car
          Name: 车模型
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/Group
          Displays:
            - Class: myrvizplugin/Speed
              Enabled: true
              Name: Speed
              Topic: actuator
              Value: true
              auto color change: false
              background color: 0; 0; 0
              backround alpha: 0
              foreground alpha: 0.699999988079071
              foreground alpha 2: 0.4000000059604645
              foreground color: 25; 255; 240
              left: 10
              max color: 255; 0; 0
              max value: 1
              min value: 0
              name: km/h
              show caption: true
              size: 90
              text size: 14
              top: 5
            - Class: myrvizplugin/Status
              Enabled: false
              Name: Status
              Topic: monitor
              Value: false
              auto color change: false
              background color: 0; 0; 0
              backround alpha: 0
              foreground alpha: 0.6000000238418579
              foreground alpha 2: 0.4000000059604645
              foreground color: 25; 255; 240
              left: -10
              max color: 255; 0; 0
              max value: 1
              min value: 0
              show caption: true
              size: 500
              text size: 14
              top: 120
            - Class: myrvizplugin/Geer
              Enabled: true
              Name: Geer档位
              Topic: actuator
              Value: true
              auto color change: false
              background color: 0; 0; 0
              backround alpha: 1
              foreground alpha: 0.699999988079071
              foreground alpha 2: 0.4000000059604645
              foreground color: 25; 255; 240
              left: 60
              max color: 255; 0; 0
              max value: 1
              min value: 0
              name: 油门
              show caption: true
              size: 150
              text size: 6
              top: 510
            - Align Bottom: false
              Background Alpha: 0.800000011920929
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 25; 255; 240
              Invert Shadow: false
              Name: 耗时
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: false
              Topic: /elapsedtime
              Value: true
              font: DejaVu Sans Mono
              height: 128
              left: 0
              line width: 2
              text size: 12
              top: 0
              width: 128
            - Class: myrvizplugin/Steer
              Enabled: true
              Name: 转向
              Topic: actuator
              Value: true
              auto color change: false
              background color: 0; 0; 0
              backround alpha: 0
              foreground alpha: 0.699999988079071
              foreground alpha 2: 0.4000000059604645
              foreground color: 25; 255; 240
              left: 120
              max color: 255; 0; 0
              max value: 1
              min value: 0
              show caption: true
              size: 90
              text size: 14
              top: 5
            - Class: myrvizplugin/Bar
              Enabled: true
              Name: Bar油门
              Topic: controllon
              Value: true
              auto color change: false
              background color: 48; 48; 48
              backround alpha: 0
              foreground alpha: 0.699999988079071
              foreground alpha 2: 0.4000000059604645
              foreground color: 25; 255; 240
              left: 0
              max color: 255; 0; 0
              max value: 1
              min value: 0
              name: 油门
              show caption: true
              size: 150
              text size: 14
              top: 400
          Enabled: true
          Name: 车身
        - Class: rviz/Group
          Displays:
            - Class: rviz/Marker
              Enabled: true
              Marker Topic: /dash_line_left
              Name: Marker
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: rviz/Marker
              Enabled: true
              Marker Topic: /dash_line_right
              Name: Marker
              Namespaces:
                {}
              Queue Size: 100
              Value: true
          Enabled: true
          Name: 地图
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 204; 0; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: /rsbg/path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /path
          Unreliable: false
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.30000001192092896
          Name: Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /path111
          Unreliable: false
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /objects
          Name: planningobjects
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/Group
          Displays:
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath0
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath0
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath1
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath1
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath2
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath2
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath3
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath3
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath4
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath4
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath5
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath5
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath6
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath6
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath7
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath7
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath8
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath8
              Unreliable: false
              Value: true
            - Alpha: 1
              Buffer Length: 1
              Class: rviz/Path
              Color: 239; 41; 41
              Enabled: true
              Head Diameter: 0.30000001192092896
              Head Length: 0.20000000298023224
              Length: 0.30000001192092896
              Line Style: Lines
              Line Width: 0.029999999329447746
              Name: collsionPath9
              Offset:
                X: 0
                Y: 0
                Z: 0
              Pose Color: 255; 85; 255
              Pose Style: None
              Queue Size: 10
              Radius: 0.029999999329447746
              Shaft Diameter: 0.10000000149011612
              Shaft Length: 0.10000000149011612
              Topic: /collsionPath9
              Unreliable: false
              Value: true
          Enabled: false
          Name: collsionPath
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /sensorobjects_track
          Name: sensorobjects_track
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: false
      Name: interaction
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: car
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Angle: -6.180039882659912
      Class: rviz/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 45.08088302612305
      Target Frame: <Fixed Frame>
      X: -6.583633899688721
      Y: 22.028974533081055
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 848
  Hide Left Dock: true
  Hide Right Dock: false
  QMainWindow State: 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
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1086
  X: 628
  Y: 27
  image_detect_results:
    collapsed: false
  usbcamera0:
    collapsed: false
  wj_camera_0:
    collapsed: false
  wj_camera_3:
    collapsed: false
