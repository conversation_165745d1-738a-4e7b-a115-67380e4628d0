<launch>
    <!-- 前后lidar -->
    <!-- <include file="$(find livox_ros_driver)/launch/livox_lidar.launch"/>
    <include file="$(find sensorlidarrs)/launch/deal_lidarrs.launch"/>
    <include file="$(find sensorlidarrsback)/launch/deal_lidarrsback.launch"/> -->
    <!-- radar -->
     <include file="$(find sensorradar)/launch/sensorradar.launch"/>  <!--  红旗2-->

    <!-- 融合跟踪 -->
    <include file="$(find fusiontracking)/launch/fusiontracking.launch"/>

    <!-- 车路融合 -->
<!--    <include file="$(find v2ifusion)/launch/v2ifusion.launch"/>-->

    <!-- 定位 -->
    <include file="$(find sensorgps)/launch/sensorgps.launch"/>

    <!--   定位节点  -->
    <!--<include file="$(find ndt_localizer)/launch/my_map.launch"/>-->
    <!--<include file="$(find ndt_localizer)/launch/my_localization.launch"/>-->

</launch>
