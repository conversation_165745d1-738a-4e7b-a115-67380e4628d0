# 车路融合时间同步配置文件
time_sync:
  # 基础参数
  base_offset_ms: 360.0          # 基础时间偏移(ms) - 车端帧尾vs路端帧头的理论差值
  frame_duration_ms: 100.0       # 帧持续时间(ms) - 10Hz对应100ms
  max_time_gap_ms: 400.0         # 最大允许时间差(ms)
  
  # 自适应参数
  enable_adaptive: true          # 是否启用自适应时间同步
  adaptation_rate: 0.1           # 自适应学习率 (0.0-1.0)
  history_size: 50               # 时间差历史记录大小
  outlier_threshold: 3.0         # 异常值阈值(标准差倍数)
  max_dynamic_offset_ms: 200.0   # 最大动态偏移范围(ms)
  
  # 网络延迟估计
  enable_network_delay_estimation: true  # 是否启用网络延迟估计
  network_delay_smoothing: 0.3           # 网络延迟平滑因子
  max_network_delay_ms: 500.0            # 最大网络延迟(ms)
  packet_timeout_ms: 5000.0              # 数据包超时时间(ms)
  
  # 数据包管理
  enable_packet_management: true         # 是否启用数据包管理
  packet_buffer_size: 100               # 数据包缓存大小
  incomplete_packet_timeout_ms: 1000.0  # 不完整数据包超时时间(ms)
  
  # 调试和日志
  enable_debug_logging: true            # 是否启用调试日志
  log_interval: 10                      # 日志输出间隔(帧数)
  save_sync_statistics: false          # 是否保存同步统计数据
  statistics_file_path: "/tmp/time_sync_stats.csv"
  
  # 质量控制
  min_data_points_for_adaptation: 10    # 自适应所需的最小数据点数
  stability_check_window: 20            # 稳定性检查窗口大小
  max_std_deviation_ms: 50.0            # 最大允许标准差(ms)
  
  # 特殊场景处理
  handle_packet_loss: true              # 是否处理丢包情况
  handle_burst_traffic: true            # 是否处理突发流量
  emergency_fallback_offset_ms: 360.0   # 紧急情况下的回退偏移值
