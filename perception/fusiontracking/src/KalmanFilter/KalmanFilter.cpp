/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2023-10-09 20:56:27
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2024-10-10 13:08:19
 * @FilePath: /HongQi2/autodriving0928/src/perception/fusiontracking/src/KalmanFilter/KalmanFilter.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEX
 */
/********************************************************************************
* @author: shuangquan han
* @date: 2022/8/31 下午1:34
* @version: 1.0
* @description: 
********************************************************************************/


#include "KalmanFilter.h"

KalmanFilter::KalmanFilter() {
	isInitialized_ = false;
}

KalmanFilter::~KalmanFilter(){

};

KalmanFilter::KalmanFilter(const KalmanFilter& other):
	isInitialized_(other.isInitialized_),
	x_(other.x_),
	P_(other.P_),
	F_(other.F_),
	Q_(other.Q_),
	H_(other.H_),
	R_(other.R_),
	K_(other.K_){

}


KalmanFilter& KalmanFilter::operator=(const KalmanFilter& other){
	if (this != &other)
    {
        isInitialized_ = other.isInitialized_;
        x_ = other.x_;
        P_ = other.P_;
        F_ = other.F_;
        Q_ = other.Q_;
        H_ = other.H_;
        R_ = other.R_;
        K_ = other.K_;
    }
    return *this;
}

void KalmanFilter::Initialization(const Eigen::VectorXd &X_in) {
	x_ = X_in;
}

bool KalmanFilter::IsInitialized() {
	return isInitialized_;
}



void KalmanFilter::setF(const Eigen::MatrixXd &F_in) {
	F_ = F_in;
}

void KalmanFilter::setP(const Eigen::MatrixXd &P_in) {
	P_ = P_in;
}

void KalmanFilter::setQ(const Eigen::MatrixXd &Q_in) {
	Q_ = Q_in;
}

void KalmanFilter::setH(const Eigen::MatrixXd &H_in) {
	H_ = H_in;
}

void KalmanFilter::setR(const Eigen::MatrixXd &R_in) {
	R_ = R_in;
}

void KalmanFilter::setX(const double& positionX){
	x_[0] = positionX;
}
void KalmanFilter::setY(const double& positionY){
	x_[1] = positionY;
}


void KalmanFilter::setHeading(const double& headingRad){
	x_[5] = headingRad;
}

void KalmanFilter::setVx(const double& relativeVx){
	x_[6] = relativeVx;
}
void KalmanFilter::setVy(const double& relativeVy){
	x_[7] = relativeVy;
}

void KalmanFilter::setAx(const double& Ax){
	x_[8] = Ax;
}
void KalmanFilter::setAy(const double& Ay){
	x_[9] = Ay;
}


void KalmanFilter::prediction() {
	x_ = F_ * x_;
	Eigen::MatrixXd Ft = F_.transpose();
	P_ = F_ * P_ * Ft + Q_;
	// 协方差矩阵正则化
    P_ = 0.5*(P_ + P_.transpose()); // 保持对称性
    P_ += 1e-4 * Eigen::MatrixXd::Identity(P_.rows(), P_.cols()); // 防止奇异
}

void KalmanFilter::kfUpdate(const Eigen::VectorXd &z) {
	Eigen::VectorXd y = z - H_ * x_;
	Eigen::MatrixXd Ht = H_.transpose();
	Eigen::MatrixXd S = H_ * P_ * Ht + R_;
	Eigen::MatrixXd Si = S.inverse();
	K_ = P_ * Ht * Si;
	
	x_ = x_ + (K_ * y);
	int x_size = x_.size();
	Eigen::MatrixXd I = Eigen::MatrixXd::Identity(x_size,x_size);
	P_ = (I - K_ * H_) * P_;
	// 协方差矩阵正则化
    P_ = 0.5*(P_ + P_.transpose()); // 保持对称性
    P_ += 1e-4 * Eigen::MatrixXd::Identity(P_.rows(), P_.cols()); // 防止奇异
}

Eigen::VectorXd KalmanFilter::getX() {
	double headingRadTemp = x_(5);
	double headingRad = fmod(x_(5), 2.0 * M_PI);
	// 处理负值的情况
	if (headingRad < 0) {
		headingRad += (2 * M_PI);
	}
	// 处理接近零的小负值
    if (std::abs(headingRad) < 1e-9) {
        headingRad = 0.0;
    }

	x_(5) = headingRad; 
	
	if(x_(5) < 0 || x_(5) >= 2.0 * M_PI){
		// std::cout <<"heading 异常: raw Rad = " << headingRadTemp  
		// 	<<", raw degree = " << headingRadTemp  * 180.0/ M_PI 
		// 	<<", now  degree unnormal = " << x_(5) * 180.0/ M_PI << std::endl;
		// assert(x_(5) >= 0 && x_(5) < 2.0 * M_PI);
	}

	Eigen::VectorXd xTemp = x_;
	return xTemp;
}

Eigen::MatrixXd KalmanFilter::getP(){
	Eigen::MatrixXd PTemp = P_;
	return PTemp;
}

Eigen::MatrixXd KalmanFilter::getQ(){
	return Q_;
}

Eigen::MatrixXd KalmanFilter::getR(){
	return R_;
}

Eigen::MatrixXd KalmanFilter::getK(){
	return K_;
}