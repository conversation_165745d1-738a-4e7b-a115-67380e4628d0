/********************************************************************************
* @author: shuangquan han
* @date: 2023/6/21 下午5:48
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_FUSIONTRACKING_H
#define SRC_FUSIONTRACKING_H

#include <iostream>
#include <mutex>
#include <deque>
#include <vector>
#include <thread>

#include <eigen3/Eigen/Dense>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl_ros/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/transforms.h>
#include <opencv2/opencv.hpp>

#include <spdlog/spdlog.h>

#include <ros/ros.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/sensorgps.h"
#include "common_msgs/cloudpant.h"
#include "common_msgs/cloudpants.h"
#include "common_msgs/elapsedtime.h"
#include "common_msgs/decisionbehavior.h"

#include "../../commonlibrary/src/common.h"
#include "../../commonlibrary/src/Hungarian.h"
#include "../../commonlibrary/src/getiou.h"
#include "../../commonlibrary/src/computeAssignmentMatrix.h"
#include "../../commonlibrary/src/coordinateTransformation/wgs84_utm.h"
#include "../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h" //从FusionAndTrk/src目录开始算再去找头文件路径
#include "../../commonlibrary/src/visualize/sensorobjectsviewer.h"
#include "../../commonlibrary/src/visualize/viewer.h"
#include "../../commonlibrary/src/configManager/configManager.h"
#include "ObjectFusionTracking.h"
// #include "./sensorobjects/cameraobjects.h"
#include "../../commonlibrary/src/sensorobjects/cameraobjects.h"
#include "../../commonlibrary/src/logger/logger.h"
#include "../../commonlibrary/src/fileWriter/fileWriter.h"


using namespace std;
typedef long int64;

const static float   ANGLE_FACTOR = 0.017453; //m_pi/180.0


class FusionTracking {
public:
	FusionTracking(ros::NodeHandle nh);
	~FusionTracking();

	ros::NodeHandle m_nh;
	void run();
	void fusiontracking();
	
	////// 订阅话题
	ros::Subscriber sub_lidar;
	ros::Subscriber sub_radar;
	ros::Subscriber sub_gps;
	ros::Subscriber sub_behaviordecision;
	//ros::Subscriber sub_obu;
	ros::Subscriber sub_slamgps;
	ros::Subscriber sub_cloudobjects;

	////// 发布话题
	ros::Publisher pub_lidarObjectBBX;
	ros::Publisher pub_radarObjectBBX;
	ros::Publisher pub_fusionObjectBBX;
	ros::Publisher pub_trackingObjectBBX;
	ros::Publisher pub_fusiontrackingElapsedtime;
	ros::Publisher pub_radarCloudRaw;//发布radar中心点
	ros::Publisher pub_cloudObjectsBBX;
	ros::Publisher pub_v2iFusionObjectBBX;

	////// 回调函数
	///***receive data from sensorlidar topic***/
	void SubCallback_lidar(const common_msgs::sensorobjects::ConstPtr &msg);
	///***receive data from sensorradar topic***/
	void SubCallback_radar(const common_msgs::sensorobjects::ConstPtr &msg);
	///***receive  Data from sensorgps node  ***/
	void SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg);
	void SubCallback_cloudpants(const common_msgs::cloudpants::ConstPtr &msg);
	void SubCallback_slamgps(const common_msgs::sensorgps::ConstPtr &msg);
	
	void SubCallback_behaviordecision(const common_msgs::decisionbehavior::ConstPtr& behaviordecisionMsg);
	void sensorTimeSynchro();
	
	//////功能函数
	template <typename T>
	void timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag);
	bool isClusterObjectsDelete(common_msgs::sensorobject& clusterObject);
	bool isObjectInHDMap(common_msgs::sensorobject& lidarObject);
	void LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y);
	void transformToAbsoluteCoordinates(float x, float y, float x_car, float y_car, float heading_car,
												 float& x_obj, float& y_obj);
	bool InMap(float x ,float y, float carx, float cary);
	void transRadar_carFrontRFU2CarBackRFU(common_msgs::sensorobjects& msg);
	void sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source, const int& sensorType);
	float getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                            const common_msgs::point3d& point);
	void findOptimalYaw(common_msgs::sensorobject& lidarObject);
	void cloudpantsPreprocess(const common_msgs::cloudpants& cloudpants, common_msgs::sensorobjects& cloudObjects);
	int transCloudObjectType2CameraDetectionType(const int& cloudObjectType);
	common_msgs::sensorobject transSensorgps2carBackRFU();
	
private:
	using c_cSenObjectsViewer = VISUALIZATION::SensorObjectsViewer;
	Common m_common;
	wgs84_utm wgs84Utm;
	ObjectFusionTracking m_objectFusionTracking;
	SensorAxisTransformation m_sensorAxisTransformer;
	boost::shared_ptr<c_cSenObjectsViewer> m_pcMatchedRadarObjectsViewer;
	boost::shared_ptr<c_cSenObjectsViewer> m_pcUnmatchedRadarObjectsViewer;
	boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
	std::shared_ptr<Logger> m_pcLogger;
	std::shared_ptr<spdlog::logger> m_pLogger;
	boost::shared_ptr<FileWriter> m_pTimeUseFileWriter;
	
	////参数
	cv::Mat m_hdMapPicture;
	
	//外参
	float m_lidar2carBackRFU_X;
	float m_lidar2carBackRFU_Y;
	float m_lidar2carBackRFU_Z;
	
	float m_lidarFLU2carBackRFU_rollDegree;
	float m_lidarFLU2carBackRFU_pitchDegree;
	float m_lidarFLU2carBackRFU_yawDegree;
	
	float m_lidarRFU2carBackRFU_rollDegree;
	float m_lidarRFU2carBackRFU_pitchDegree;
	float m_lidarRFU2carBackRFU_yawDegree;
	
	Eigen::Vector3d m_lidarRFU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidarFLU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidar2carBackRFU_translation;

	
	//// 当前帧数据
	common_msgs::sensorobjects m_lidarMsg;      //lidar data
	common_msgs::sensorobjects m_radarMsg;      //radar data
	common_msgs::sensorgps     m_gpsMsg ;       // gps data
	common_msgs::sensorgps     m_slamgpsMsg ;       // gps data
	common_msgs::cloudpants m_cloudpantsMsg; //cloudpants data
	common_msgs::sensorobjects m_cloudObjectMsg; //cloudpants data
	common_msgs::decisionbehavior m_behaviordecisionMsg;
	
	std::vector<double> m_selfCarSpeed;
	
	//锁变量
	std::mutex m_lidarMutex;
	std::mutex m_radarMutex;
	std::mutex m_gpsMutex;
	std::mutex m_slamgpsMutex;
	std::mutex m_cloudpantsMutex;
	std::mutex m_behaviordecisionMutex;
	
	////数据容器
	std::deque<common_msgs::sensorobjects> m_lidarMsgDeque_;
	std::deque<common_msgs::sensorobjects> m_radarMsgDeque_;
	std::deque<common_msgs::sensorgps> m_gpsMsgDeque_;
	std::deque<common_msgs::sensorgps> m_slamgpsMsgDeque_;
	std::vector<common_msgs::sensorobject> m_radarVector;//
	std::deque<common_msgs::cloudpants> m_cloudpantsMsgDeque_;

	long m_gpstime; //gps时间
	long m_curLidarStamp;
};


#endif //SRC_FUSIONTRACKING_H
