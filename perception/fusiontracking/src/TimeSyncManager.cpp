/*
 * @Author: AI Assistant
 * @Date: 2025-01-03
 * @Description: 车路融合时间同步管理器实现
 */

#include "TimeSyncManager.h"
#include <yaml-cpp/yaml.h>
#include <iostream>
#include <iomanip>
#include <numeric>

TimeSyncManager::TimeSyncManager() 
    : m_dynamic_offset(0.0f)
    , m_network_delay(0.0f)
    , m_frame_count(0)
    , m_is_initialized(false) {
    
    m_time_gap_history.reserve(m_config.history_size);
    m_last_update_time = ros::Time::now();
}

TimeSyncManager::~TimeSyncManager() {
    if (m_stats_file.is_open()) {
        m_stats_file.close();
    }
}

bool TimeSyncManager::initialize(const std::string& config_file_path, 
                                std::shared_ptr<spdlog::logger> logger) {
    m_logger = logger;
    
    // 加载配置文件
    if (!config_file_path.empty() && !loadConfig(config_file_path)) {
        if (m_logger) {
            m_logger->warn("无法加载时间同步配置文件: {}, 使用默认配置", config_file_path);
        }
    }
    
    // 初始化统计文件
    if (m_config.save_sync_statistics) {
        m_stats_file.open(m_config.statistics_file_path, std::ios::out | std::ios::app);
        if (m_stats_file.is_open()) {
            m_stats_file << "timestamp,vehicle_time,roadside_time,time_gap,optimal_offset,"
                        << "dynamic_offset,network_delay,sync_quality,is_outlier\n";
        }
    }
    
    m_is_initialized = true;
    
    if (m_logger) {
        m_logger->info("时间同步管理器初始化完成 - 基础偏移: {:.1f}ms, 自适应: {}", 
                      m_config.base_offset_ms, m_config.enable_adaptive ? "启用" : "禁用");
    }
    
    return true;
}

float TimeSyncManager::calculateOptimalOffset(float vehicle_timestamp, float roadside_timestamp) {
    if (!m_is_initialized) {
        return m_config.base_offset_ms;
    }
    
    // 计算原始时间差
    float raw_time_gap = vehicle_timestamp - roadside_timestamp;
    
    // 更新数据包信息
    float current_time = ros::Time::now().toSec() * 1000.0f;
    updatePacketInfo(roadside_timestamp, current_time);
    
    // 估计网络延迟
    float estimated_network_delay = estimateNetworkDelay();
    
    // 更新时间差统计
    updateTimeGapStatistics(raw_time_gap);
    
    // 计算自适应偏移
    float adaptive_offset = calculateAdaptiveOffset();
    
    // 综合计算最优偏移
    float optimal_offset = m_config.base_offset_ms + adaptive_offset + estimated_network_delay;
    
    // 更新统计信息
    m_statistics.current_dynamic_offset = adaptive_offset;
    m_statistics.estimated_network_delay = estimated_network_delay;
    m_statistics.sync_quality_score = calculateSyncQualityScore();
    m_statistics.is_stable = checkSyncStability();
    
    // 记录统计数据
    if (m_config.save_sync_statistics && m_stats_file.is_open()) {
        bool is_outlier = isOutlier(raw_time_gap);
        m_stats_file << std::fixed << std::setprecision(3)
                    << current_time << "," << vehicle_timestamp << "," << roadside_timestamp << ","
                    << raw_time_gap << "," << optimal_offset << "," << adaptive_offset << ","
                    << estimated_network_delay << "," << m_statistics.sync_quality_score << ","
                    << (is_outlier ? 1 : 0) << "\n";
        m_stats_file.flush();
    }
    
    // 调试日志
    m_frame_count++;
    if (m_config.enable_debug_logging && m_logger && (m_frame_count % m_config.log_interval == 0)) {
        m_logger->debug("时间同步状态 - 原始差值: {:.1f}ms, 网络延迟: {:.1f}ms, "
                       "自适应偏移: {:.1f}ms, 最优偏移: {:.1f}ms, 质量评分: {:.2f}", 
                       raw_time_gap, estimated_network_delay, adaptive_offset, 
                       optimal_offset, m_statistics.sync_quality_score);
    }
    
    return optimal_offset;
}

void TimeSyncManager::updatePacketInfo(float timestamp, float receive_time, 
                                      int packet_id, int total_packets, bool is_complete) {
    if (!m_config.enable_packet_management) {
        return;
    }
    
    PacketInfo packet_info;
    packet_info.timestamp = timestamp;
    packet_info.receive_time = receive_time;
    packet_info.packet_id = packet_id;
    packet_info.total_packets = total_packets;
    packet_info.is_complete = is_complete;
    packet_info.processing_delay = 50.0f; // 假设处理延迟50ms
    
    m_packet_buffer[timestamp].push_back(packet_info);
    m_statistics.total_packets++;
    
    // 管理缓存大小
    managePacketBuffer();
}

TimeSyncManager::SyncStatistics TimeSyncManager::getSyncStatistics() const {
    return m_statistics;
}

bool TimeSyncManager::isSyncQualityGood() const {
    return m_statistics.sync_quality_score > 0.7f && 
           m_statistics.std_time_gap < m_config.max_std_deviation_ms &&
           m_statistics.is_stable;
}

void TimeSyncManager::reset() {
    m_time_gap_history.clear();
    m_packet_buffer.clear();
    m_dynamic_offset = 0.0f;
    m_network_delay = 0.0f;
    m_frame_count = 0;
    
    // 重置统计信息
    m_statistics = SyncStatistics();
    
    if (m_logger) {
        m_logger->info("时间同步管理器已重置");
    }
}

bool TimeSyncManager::saveSyncStatistics(const std::string& file_path) const {
    std::string path = file_path.empty() ? m_config.statistics_file_path + ".summary" : file_path;
    
    std::ofstream file(path);
    if (!file.is_open()) {
        return false;
    }
    
    file << "=== 时间同步统计报告 ===\n";
    file << "平均时间差: " << m_statistics.avg_time_gap << " ms\n";
    file << "时间差标准差: " << m_statistics.std_time_gap << " ms\n";
    file << "当前动态偏移: " << m_statistics.current_dynamic_offset << " ms\n";
    file << "估计网络延迟: " << m_statistics.estimated_network_delay << " ms\n";
    file << "总包数: " << m_statistics.total_packets << "\n";
    file << "异常包数: " << m_statistics.outlier_packets << "\n";
    file << "同步质量评分: " << m_statistics.sync_quality_score << "\n";
    file << "同步状态: " << (m_statistics.is_stable ? "稳定" : "不稳定") << "\n";
    
    file.close();
    return true;
}

bool TimeSyncManager::loadConfig(const std::string& config_file_path) {
    try {
        YAML::Node config = YAML::LoadFile(config_file_path);
        
        if (config["time_sync"]) {
            auto ts_config = config["time_sync"];
            
            // 加载基础参数
            if (ts_config["base_offset_ms"]) 
                m_config.base_offset_ms = ts_config["base_offset_ms"].as<float>();
            if (ts_config["frame_duration_ms"]) 
                m_config.frame_duration_ms = ts_config["frame_duration_ms"].as<float>();
            if (ts_config["max_time_gap_ms"]) 
                m_config.max_time_gap_ms = ts_config["max_time_gap_ms"].as<float>();
            
            // 加载自适应参数
            if (ts_config["enable_adaptive"]) 
                m_config.enable_adaptive = ts_config["enable_adaptive"].as<bool>();
            if (ts_config["adaptation_rate"]) 
                m_config.adaptation_rate = ts_config["adaptation_rate"].as<float>();
            if (ts_config["history_size"]) 
                m_config.history_size = ts_config["history_size"].as<size_t>();
            if (ts_config["outlier_threshold"]) 
                m_config.outlier_threshold = ts_config["outlier_threshold"].as<float>();
            if (ts_config["max_dynamic_offset_ms"]) 
                m_config.max_dynamic_offset_ms = ts_config["max_dynamic_offset_ms"].as<float>();
            
            // 加载其他参数...
            if (ts_config["enable_debug_logging"]) 
                m_config.enable_debug_logging = ts_config["enable_debug_logging"].as<bool>();
            if (ts_config["log_interval"]) 
                m_config.log_interval = ts_config["log_interval"].as<int>();
            if (ts_config["save_sync_statistics"]) 
                m_config.save_sync_statistics = ts_config["save_sync_statistics"].as<bool>();
            if (ts_config["statistics_file_path"]) 
                m_config.statistics_file_path = ts_config["statistics_file_path"].as<std::string>();
        }
        
        return true;
    } catch (const std::exception& e) {
        if (m_logger) {
            m_logger->error("加载时间同步配置文件失败: {}", e.what());
        }
        return false;
    }
}

void TimeSyncManager::updateTimeGapStatistics(float time_gap) {
    // 检查是否为异常值
    if (m_time_gap_history.size() > m_config.min_data_points_for_adaptation && isOutlier(time_gap)) {
        m_statistics.outlier_packets++;
        if (m_logger && m_config.enable_debug_logging) {
            m_logger->warn("检测到时间差异常值: {:.1f}ms，忽略此次更新", time_gap);
        }
        return;
    }

    // 添加到历史记录
    m_time_gap_history.push_back(time_gap);

    // 保持历史记录大小
    if (m_time_gap_history.size() > m_config.history_size) {
        m_time_gap_history.erase(m_time_gap_history.begin());
    }

    // 计算统计量
    if (m_time_gap_history.size() >= 5) {
        float sum = std::accumulate(m_time_gap_history.begin(), m_time_gap_history.end(), 0.0f);
        m_statistics.avg_time_gap = sum / m_time_gap_history.size();

        // 计算标准差
        float variance = 0.0f;
        for (float gap : m_time_gap_history) {
            variance += (gap - m_statistics.avg_time_gap) * (gap - m_statistics.avg_time_gap);
        }
        m_statistics.std_time_gap = std::sqrt(variance / m_time_gap_history.size());
    }
}

bool TimeSyncManager::isOutlier(float time_gap) const {
    if (m_time_gap_history.size() < m_config.min_data_points_for_adaptation) {
        return false; // 数据不足，不判断异常值
    }

    float deviation = std::abs(time_gap - m_statistics.avg_time_gap);
    return deviation > (m_config.outlier_threshold * m_statistics.std_time_gap);
}

float TimeSyncManager::calculateAdaptiveOffset() {
    if (!m_config.enable_adaptive ||
        m_time_gap_history.size() < m_config.min_data_points_for_adaptation) {
        return m_dynamic_offset;
    }

    // 计算期望的时间差（考虑帧持续时间的一半，因为车端用帧尾，路端用帧头）
    float expected_gap = m_config.frame_duration_ms / 2.0f;

    // 计算当前平均时间差与期望值的偏差
    float gap_error = m_statistics.avg_time_gap - expected_gap;

    // 自适应调整动态偏移
    float adjustment = gap_error * m_config.adaptation_rate;
    m_dynamic_offset -= adjustment; // 负反馈调整

    // 限制动态偏移的范围
    m_dynamic_offset = std::max(-m_config.max_dynamic_offset_ms,
                               std::min(m_config.max_dynamic_offset_ms, m_dynamic_offset));

    return m_dynamic_offset;
}

float TimeSyncManager::estimateNetworkDelay() {
    if (!m_config.enable_network_delay_estimation || m_packet_buffer.empty()) {
        return m_network_delay;
    }

    std::vector<float> delays;
    float current_time = ros::Time::now().toSec() * 1000.0f;

    // 计算最近的网络延迟
    int count = 0;
    for (auto it = m_packet_buffer.rbegin(); it != m_packet_buffer.rend() && count < 10; ++it) {
        for (const auto& packet : it->second) {
            if (current_time - packet.receive_time < 1000.0f) { // 只考虑1秒内的数据
                float estimated_delay = packet.receive_time - packet.timestamp - packet.processing_delay;
                if (estimated_delay > 0 && estimated_delay < m_config.max_network_delay_ms) {
                    delays.push_back(estimated_delay);
                    count++;
                }
            }
        }
    }

    if (delays.empty()) {
        return m_network_delay; // 返回上次的估计值
    }

    // 计算平均网络延迟
    float sum = std::accumulate(delays.begin(), delays.end(), 0.0f);
    float avg_delay = sum / delays.size();

    // 平滑更新网络延迟估计
    m_network_delay = m_network_delay * (1 - m_config.network_delay_smoothing) +
                     avg_delay * m_config.network_delay_smoothing;

    return m_network_delay;
}

void TimeSyncManager::managePacketBuffer() {
    if (!m_config.enable_packet_management) {
        return;
    }

    // 清理过期的数据包信息
    float current_time = ros::Time::now().toSec() * 1000.0f;
    auto it = m_packet_buffer.begin();
    while (it != m_packet_buffer.end()) {
        if (current_time - it->first > m_config.packet_timeout_ms) {
            it = m_packet_buffer.erase(it);
        } else {
            ++it;
        }
    }

    // 限制缓存大小
    while (m_packet_buffer.size() > m_config.packet_buffer_size) {
        m_packet_buffer.erase(m_packet_buffer.begin());
    }
}

float TimeSyncManager::calculateSyncQualityScore() const {
    if (m_time_gap_history.size() < m_config.min_data_points_for_adaptation) {
        return 0.0f;
    }

    // 基于标准差的质量评分
    float std_score = std::max(0.0f, 1.0f - m_statistics.std_time_gap / m_config.max_std_deviation_ms);

    // 基于异常值比例的质量评分
    float outlier_ratio = static_cast<float>(m_statistics.outlier_packets) / m_statistics.total_packets;
    float outlier_score = std::max(0.0f, 1.0f - outlier_ratio * 5.0f); // 异常值比例越低越好

    // 基于稳定性的质量评分
    float stability_score = checkSyncStability() ? 1.0f : 0.5f;

    // 综合评分
    return (std_score * 0.5f + outlier_score * 0.3f + stability_score * 0.2f);
}

bool TimeSyncManager::checkSyncStability() const {
    if (m_time_gap_history.size() < m_config.stability_check_window) {
        return false;
    }

    // 检查最近窗口内的标准差是否稳定
    size_t window_size = std::min(static_cast<size_t>(m_config.stability_check_window),
                                 m_time_gap_history.size());

    auto start_it = m_time_gap_history.end() - window_size;
    std::vector<float> recent_gaps(start_it, m_time_gap_history.end());

    float sum = std::accumulate(recent_gaps.begin(), recent_gaps.end(), 0.0f);
    float mean = sum / recent_gaps.size();

    float variance = 0.0f;
    for (float gap : recent_gaps) {
        variance += (gap - mean) * (gap - mean);
    }
    float std_dev = std::sqrt(variance / recent_gaps.size());

    return std_dev < m_config.max_std_deviation_ms * 0.5f; // 稳定性要求更严格
}
