/********************************************************************************
* @author: shuangquan han
* @date: 2023/10/31 上午10:13
* @version: 1.0
* @description: 
********************************************************************************/


#include "ObjectTracking.h"


int ObjectTracking::m_kf_count = 1;
common_msgs::sensorgps ObjectTracking::m_selfCarGPS;
std::vector<double> ObjectTracking::m_selfCarUTMPosition;
std::vector<double> ObjectTracking::m_selfCarSpeed;
SensorAxisTransformation ObjectTracking::m_sensorAxisTransformation;
boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
std::shared_ptr<spdlog::logger> m_pLogger;

double ObjectTracking::m_curTimestamp;
double ObjectTracking::m_lastTimestamp;
enum TrackingState{
	Initial = 0,
	Predict = 1,
	Update = 2,
	TRACKING_STATE_LOST = 3,
	TRACKING_STATE_DETECTED = 4,
	TRACKING_STATE_MERGED_DETECTED = 5,
	TRACKING_STATE_MERGED_LOST = 6,
};

void ObjectTracking::setConfigManager(boost::shared_ptr<ConfigManager::ConfigManager>& pConfigManager){
	m_pConfigManager = pConfigManager;
}

void ObjectTracking::setLoggerManager(const std::shared_ptr<spdlog::logger>& pLogger){
	m_pLogger = pLogger;
}

void ObjectTracking::setStaticMembers(const common_msgs::sensorgps& selfCarGPS,
                                      const std::vector<double>& selfCarUTMPosition,
                                      const std::vector<double>& selfCarSpeed,
									  const SensorAxisTransformation& sensorAxisTransformer,
									  const double& curTimestamp){
	m_selfCarGPS = selfCarGPS;
	m_selfCarSpeed = selfCarSpeed;
	m_selfCarUTMPosition = selfCarUTMPosition;
	m_sensorAxisTransformation = sensorAxisTransformer;
	

	static bool m_isInitial = true;
	if(m_isInitial){
		m_lastTimestamp = curTimestamp;
		// m_curTimestamp = curTimestamp;
		m_isInitial = false;
	}
	else{
		// m_lastTimestamp = m_curTimestamp;
	}
	m_curTimestamp = curTimestamp;
}


int ObjectTracking::generateNewTrackId() {
  int ret_track_id = static_cast<int>(m_kf_count);
  if (m_kf_count == std::numeric_limits<unsigned int>::max()) {
    m_kf_count = 1;
  } else {
    m_kf_count++;
  }
  return ret_track_id;
}


ObjectTracking::ObjectTracking(const common_msgs::sensorobject& object){
	Eigen::Vector3d bodyAxisPosition{object.x, object.y,object.z};
	
	m_centerPointUTM = m_sensorAxisTransformation.ENU2UTM(bodyAxisPosition);
	m_centerLLA.reserve(3);
	
	Eigen::Vector3d bodyAxisSpeed;
	if(object.value == COMMON::SensorType::LIDAR){
		bodyAxisSpeed << 0, 0,0;
	}
	else{
		bodyAxisSpeed << object.relspeedx+ m_selfCarSpeed[0], object.relspeedy + m_selfCarSpeed[1],0;
	};
	//对于速度来说，ENU坐标系看作是UTM坐标系
	std::vector<double> UTMRelativeSpeed = m_sensorAxisTransformation.bodyAxis2ENU(bodyAxisSpeed);
	m_absVelocityUTM = {UTMRelativeSpeed[0], UTMRelativeSpeed[1],
						UTMRelativeSpeed[2]};
						
	m_boxSize = {object.length, object.width, object.height};
	float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading);
	m_headingRadInNorthClockwise = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
	// assert(m_headingRadInNorthClockwise >= 0 && m_headingRadInNorthClockwise < 2.0 * M_PI);

	initializeKalmanFilter(object);
	m_objectOriginal = object;
	m_id = generateNewTrackId();
	m_time_since_update = 0;
	m_hits = 0;
	m_hit_streak = 0;
	m_age = 0;
	m_classification = object.classification;
	m_confidence = object.confidence;
	m_motionInfo = 0;
	m_value = object.value;
	m_objectOriginal = object;
	
	m_measureNum = m_measureNum_6;
	m_last_time_since_update = 1;
	
	m_updateCount = 0;
	m_drivingIntent = 0;
	m_radarIndex = object.radarIndex;
	m_radarObjectID = object.radarObjectID;
	//m_trackingBoxResult.resize(8);
	//m_curPredictedInfo.resize(8);
	m_historyCount = 0;
	m_isHeadingInit = false;
	m_classSwitchType = ClassSwitchType::ClassFixed;

	
	m_trackingBoxResult = {m_centerPointUTM[0], m_centerPointUTM[1],
						   object.length, object.width, object.height,
						   m_headingRadInNorthClockwise,
						   m_absVelocityUTM[0], m_absVelocityUTM[1]
						   };
	m_curPredictedInfo = {m_centerPointUTM[0], m_centerPointUTM[1],
	                      object.length, object.width, object.height,
	                      m_headingRadInNorthClockwise,
	                      m_absVelocityUTM[0], m_absVelocityUTM[1]
						};

	// cout << std::setprecision(12)
	// 	<<"构造函数跟踪目标： id = "<< m_id 
	// 	// <<", gps time = " << m_selfCarGPS.timestamp/ 1000.0
	// 	<<",  body x= "<< object.x << ", y="<< object.y
	// 	<<",  utm x= "<< m_centerPointUTM[0] << ", y="<< m_centerPointUTM[1]
	// 	// <<",  radar x= "<< object.relspeedx << ", y="<< object.relspeedx<< ", radarID ="<< static_cast<int>(object.radarIndex)
	// 	// << ", radarObjectID ="<< static_cast<int>(object.radarObjectID)
	// 	// <<",  m_absVelocityUTM x= "<< m_absVelocityUTM[0] << ", y="<< m_absVelocityUTM[1]
	// 	// <<",  m_selfCarSpeed x= "<< m_selfCarSpeed[0] << ", y="<< m_selfCarSpeed[1]
	// 	// <<",  UTMRelativeSpeed x= "<< UTMRelativeSpeed[0] << ", y="<< UTMRelativeSpeed[1]
	// 	<<",  [object.azimuth]= "<< object.azimuth * 180 / M_PI
	// 	<<",  objectAngleDegreeInNorth_Clockwise= "<< objectAngleDegreeInNorth_Clockwise
	// 	<<",  gps heading = "<< m_selfCarGPS.heading
	// 	<<",  classification = "<< static_cast<int>(object.classification)
	// 	<< endl;
	
	// std::vector<float> initialStateInfo = {m_centerPointUTM[0], m_centerPointUTM[1], object.length, object.width, object.height, m_headingRadInNorthClockwise,m_absVelocityUTM[0], m_absVelocityUTM[1]};
	// ObjectPositionInfo objectPositionInfo = packageObjectPositionInfo(initialStateInfo,
	// 																	m_curTimestamp,
	// 																	object);

	//ObjectPositionInfo objectPositionInfo;
	//objectPositionInfo.objectHeadingDegree = object.azimuth * 180 / M_PI;
	//objectPositionInfo.length = object.length;
	//objectPositionInfo.width = object.width;
	//objectPositionInfo.classification = static_cast<int>(object.classification);
	//m_historyTrajectoryInfo.historyTrajectory.clear();
	// TODO: 先不要，第一幀沒有UTM座標
	// m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
	
	m_classificationCount.resize(9); //根据common库中的LidarDetectionClassification数量确定
	if(object.classification == COMMON::LidarDetectionClassification::Zombiecar){
		m_classificationCount[8].detectedTimes++;
	}
	else{
		m_classificationCount[object.classification].detectedTimes++;
	}
	// cout<<"DEBUG: finished ObjectTracking()\n";
	
}


void ObjectTracking::initializeKalmanFilter(const common_msgs::sensorobject& object){
	m_stateNum = 10;
	
	//系统误差 Q
	// 若Q的元素远大于R的元素，则预测噪声大，从而更相信观测值，这样可能使得kalman滤波结果与观测值基本一致；
	// 反之，则更相信预测，kalman滤波结果会表现得比较规整和平滑；
	// 若二者接近，则滤波结果介于前面两者之间，根据实验效果看也缺乏实际使用价值。
	Eigen::MatrixXd Q_in = Eigen::MatrixXd(m_stateNum, m_stateNum);
	Q_in.setIdentity();
	Q_in.diagonal().setConstant(1);
	Q_in(0,0) = 0.1;
	Q_in(1,1) = 0.1;

	m_kalmanFilter.setQ(Q_in);
	//cout<<"Q_in:\n"<<Q_in<<endl;
	//	//误差协方差矩阵 对当前预测状态的信任度，它越小说明我们越相信当前预测状态
	//只是影响初始收敛速度，越小越相信当前预测状态
	//修正的最小均方误差 (P(k)): P(k)=(I-K(k)*H)*P'(k)
	Eigen::MatrixXd P_in = Eigen::MatrixXd(m_stateNum,m_stateNum);
	P_in.setZero();
	P_in.diagonal().setConstant(0.1);
	
	P_in(0,0) = 1;//x  //x y l w h yaw vx vy ax a
	P_in(1,1) = 1;//y
	P_in(2,2) = 1;//l
	P_in(3,3) = 1;//w
	P_in(4,4) = 1;//h
	P_in(5, 5) = 10;//yaw
	if((int)object.classification == COMMON::LidarDetectionClassification::Bicycle
	   || (int)object.classification == COMMON::LidarDetectionClassification::Pedestrian) //自行车或行人设置观测误差大
	{
		P_in(5, 5) = 0.1;//yaw
	}
	else if(object.value == COMMON::SensorType::OBU){
		P_in(5, 5) = 10;//yaw
	}
	else {
		P_in(5, 5) = 10;//yaw
	}
	
	if(object.value == COMMON::SensorType::RADAR || object.value == COMMON::SensorType::FUSION){//lidar-radar、lidar-radar-obu
		P_in(6,6) = 0.1;//vx
		P_in(7,7) = 0.1;//vy
	}
	else if(object.value == COMMON::SensorType::LIDAR_RADAR){
		P_in(6,6) = 1;//vx
		P_in(7,7) = 1;//vy
	}
	
	m_kalmanFilter.setP(P_in);
	//cout<<"P_in:\n"<<P_in<<endl;
	// initialize state vector with bounding box in [cx,cy,s,r] style
	//x //状态值 (x(k)): x(k)=x'(k)+K(k)*(z(k)-H*x'(k))
	//x y l w h yaw vx vy ax ay
	Eigen::VectorXd x_in = Eigen::VectorXd(m_stateNum,1);
	x_in << m_centerPointUTM[0], m_centerPointUTM[1],
			m_boxSize[0], m_boxSize[1], m_boxSize[2],
			m_headingRadInNorthClockwise, m_absVelocityUTM[0], m_absVelocityUTM[1], 0, 0;
	m_kalmanFilter.Initialization(x_in);
	// cout<<"DEBUG: finished initializeKalmanFilter()\n";
}

ObjectTracking::~ObjectTracking(){

}

vector<float> ObjectTracking::predict(const double& timeStamp,const double& timeDiff){
	// 根据类别使用不同的模型
	//匀加速模型，时间初值为0.1秒
	//x y w l h yaw vx vy ax ay
	Eigen::MatrixXd F_in = Eigen::MatrixXd(m_stateNum,m_stateNum);
	if (this->m_classification == COMMON::LidarDetectionClassification::Pedestrian
	    || this->m_classification == COMMON::LidarDetectionClassification::Cone){ // 行人匀加速 class_name = {"car", "bic", "bus", "tri", "ped", "cone", "tru", "unk"};
		//  cout<<"匀加速模型,时间间隔= "<<timeDiff<<"\n";
		F_in << 1, 0, 0, 0, 0, 0, timeDiff, 0, 0.5* pow(timeDiff,2), 0,
				0, 1, 0, 0, 0, 0, 0, timeDiff, 0, 0.5* pow(timeDiff,2),
				0, 0, 1, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 1, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 1, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 1, 0, timeDiff, 0,//需要加ax的参数
				0, 0, 0, 0, 0, 0, 0, 1, 0, timeDiff,//需要加ay的参数
				0, 0, 0, 0, 0, 0, 0, 0, 1, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 1;
		m_kalmanFilter.setF(F_in);
	}
	else{ 
		//  cout<<"匀速模型,时间间隔= "<<timeDiff<<"\n";
		F_in << 1, 0, 0, 0, 0, 0, timeDiff, 0, 0, 0,//20220909 根据时间预测
				0, 1, 0, 0, 0, 0, 0, timeDiff, 0, 0,//20220909 根据时间预测
				0, 0, 1, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 1, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 1, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 1, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 1, 0, 0, 0,//匀速 ax为0
				0, 0, 0, 0, 0, 0, 0, 1, 0, 0,//匀速 ay为0
				0, 0, 0, 0, 0, 0, 0, 0, 1, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 1;
		m_kalmanFilter.setF(F_in);
	}
	if (this->m_id == m_pConfigManager->m_debugID){
		std::vector<double> objectSpeedInCarBackRFU = getSpeedInCarbackRFU();
		cout<<"kalmantracker predict 上一帧跟踪 id1 = "<<this->m_id 
			<<", UTMx = "<< m_trackingBoxResult[0]  << ", y = "<< m_trackingBoxResult[1] 
			<<", m_trackingBoxResult[6] = "<< m_trackingBoxResult[6] << ", m_trackingBoxResult[7] = "<< m_trackingBoxResult[7] 
			<<", objectSpeedInCarBackRFU vx = "<< objectSpeedInCarBackRFU[0] << ", objectSpeedInCarBackRFU vy = "<< objectSpeedInCarBackRFU[1]
			<< ", 总共跟踪次数 = "<< this->m_hits << std::endl;
	}
	// predict
	m_kalmanFilter.prediction();//调用卡尔曼滤波
	// cout<<"kalmantracker predict id = "<<this->m_id <<", kalman predict P:\n"<< m_kalmanFilter.getP() << std::endl;
	Eigen::VectorXd statementPredicted = m_kalmanFilter.getX();
	// 限制加速度范围
	constexpr double MAX_ACCELERATION = 9.8; // 最大加速度
	statementPredicted(8) = std::min(std::max(statementPredicted(8), -MAX_ACCELERATION), MAX_ACCELERATION);
	statementPredicted(9) = std::min(std::max(statementPredicted(9), -MAX_ACCELERATION), MAX_ACCELERATION);
	m_kalmanFilter.setAx(statementPredicted(6));
	m_kalmanFilter.setAy(statementPredicted(7));

	// 限制加速度范围
	if (this->m_id == m_pConfigManager->m_debugID){
		Eigen::Vector3d speedInUTM{statementPredicted(6), statementPredicted(7), 0};
		std::vector<double> objectSpeedInCarBackRFU = getSpeedInCarbackRFU(speedInUTM);

		m_pLogger->info("kalmantracker predict 预测后 id1 = {}, UTMx = {:.2f}, y = {:.2f}, absvx = {:.2f}, absvy = {:.2f}, objectSpeedInCarBackRFU vx = {:.2f}, objectSpeedInCarBackRFU vy = {:.2f}, statementPredicted(5) = {:.2f}, 总共跟踪次数 = {}", 
						this->m_id, statementPredicted(0),statementPredicted(1),statementPredicted(6),statementPredicted(7),
						objectSpeedInCarBackRFU[0],objectSpeedInCarBackRFU[1],
						statementPredicted(5) * 180 / M_PI, this->m_hits);
	
	}
	if(statementPredicted(5) < 0 || statementPredicted(5) >= 2.0 * M_PI){
		m_pLogger->warn("heading2 异常: {:.2f}", statementPredicted(5) * 180.0/ M_PI);
	}

	// 预测速度约束
	velocitySmooth(statementPredicted);
	if(this->m_classification == COMMON::LidarDetectionClassification::Unknown){
		clusterObjectsHeadingSmooth(this->m_objectOriginal, timeStamp);
		// 聚类目标预测速度设置为0
		statementPredicted(6) = 0;
		m_kalmanFilter.setVx(statementPredicted(6));
		statementPredicted(7) = 0;
		m_kalmanFilter.setVy(statementPredicted(7));
	}

	// 预测航向角约束:当前预测与上一帧跟踪误差大
	if(cos(statementPredicted(5) - m_trackingBoxResult[5]) < 0.99){
		statementPredicted(5) = m_trackingBoxResult[5];
	}

	m_trackingBoxResult.clear();
	int statementPredictedSize = statementPredicted.size();
	for(int i = 0; i < statementPredictedSize; i++){
		m_trackingBoxResult.emplace_back(statementPredicted(i));
	}

	if (this->m_id == m_pConfigManager->m_debugID){
		std::vector<double> objectSpeedInCarBackRFU = getSpeedInCarbackRFU();
		cout<<"kalmantracker predict 速度约束后id2 = "<<this->m_id 
			<<", objectSpeedInCarBackRFU vx = "<< objectSpeedInCarBackRFU[0] << ", objectSpeedInCarBackRFU vy = "<< objectSpeedInCarBackRFU[1]
			<<", 约束后m_trackingBoxResult = "<< m_trackingBoxResult[6]<< ", m_trackingBoxResult absvy = "<< m_trackingBoxResult[7]
			<< ", 总共跟踪次数 = "<< this->m_hits << std::endl;
		m_pLogger->info("kalmantracker predict 速度约束后id2 = {},  statementPredicted(5) = {:.2f},  m_trackingBoxResult[5] = {:.2f}", 
						this->m_id, statementPredicted(5) * 180 / M_PI,  m_trackingBoxResult[5] * 180 / M_PI);
	}
	
	m_age += 1;
	
	if (m_time_since_update > 0)
		m_hit_streak = 0;
	m_time_since_update += 1;//预测加1
	
	//x y w l h yaw vx vy
	m_curPredictedInfo = {statementPredicted(0), statementPredicted(1), statementPredicted(2), statementPredicted(3), statementPredicted(4), statementPredicted(5),statementPredicted(6), statementPredicted(7)};
	
	
	Eigen::Vector3d objectPositionInUTMAxis{statementPredicted(0), statementPredicted(1), this->m_objectOriginal.z};
	std::vector<double> singlePredictedTrajectory{objectPositionInUTMAxis[0], objectPositionInUTMAxis[1], objectPositionInUTMAxis[2]};//20220907 存储预测轨迹 20220913 使用UTM坐标
	ObjectPositionInfo objectPositionInfo;
	objectPositionInfo.timeStamp = timeStamp;
	objectPositionInfo.position = singlePredictedTrajectory;
	
	//20220914 保存经纬度
	wgs84_utm wgs84Utm;
	WGS84Corr lla;
	wgs84Utm.UTMXYToLatLon(singlePredictedTrajectory[0] , singlePredictedTrajectory[1], m_pConfigManager->m_cityUTMCode, false, lla);
	//std::cerr << "predict log lat =" << lla.log/M_PI*180 << " " << lla.lat/M_PI*180 << std::endl;
	objectPositionInfo.longtitude = lla.log/M_PI*180;
	objectPositionInfo.latitude = lla.lat/M_PI*180;
	objectPositionInfo.altitude = singlePredictedTrajectory[2];
	
	objectPositionInfo.rollDegree = 0;//20220920
	objectPositionInfo.pitchDegree = 0;
	objectPositionInfo.yawDegree = statementPredicted(5) * 180 / M_PI;//objectAngleDegreeInNorth_Clockwise;
	
	objectPositionInfo.absoluteSpeedX = statementPredicted(6);//20221226 用于记录历史速度 20230118 改成绝对速度 + m_selfCarSpeed[0]
	objectPositionInfo.absoluteSpeedY = statementPredicted(7);// + m_selfCarSpeed[1]

	if (this->m_id == m_pConfigManager->m_debugID)
		cout << "kalmantracker predict id = " << this->m_id << ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
			<<", 上一帧x = " << m_trackingBoxResult[0] 
			<<", 当前帧预测x = " << statementPredicted(0)
			<<", 上一帧跟踪y = " << m_trackingBoxResult[1] 
			<<", 当前帧预测y = " << statementPredicted(1)
			<<", 上一帧跟踪角度 = " << m_trackingBoxResult[5] * 180.0 / M_PI 
			<<", 当前帧预测角度 = " << statementPredicted(5) * 180.0 / M_PI 
			<<", 预测横向速度 = " << statementPredicted(6)
			<<", 预测纵向速度 = " << statementPredicted(7) 
			<<", 自车UTMvx速度 = " << m_selfCarGPS.speedE
			<<", 自车UTMvy速度 = " << m_selfCarGPS.speedN
			<<", 横向UTMvx速度 = " << objectPositionInfo.absoluteSpeedX
			<<", 纵向UTMvy速度 = " << objectPositionInfo.absoluteSpeedY 
			<<", timeDiff = " << timeDiff << std::endl;


	if(objectPositionInfo.yawDegree < 0){
		cout << "[kalmantracker predict] yawDegree = " << objectPositionInfo.yawDegree
		     << std::endl;
	}
	
	objectPositionInfo.objectHeadingDegree = m_curPredictedInfo[5] * 180 / M_PI;
	objectPositionInfo.length = m_curPredictedInfo[2];
	objectPositionInfo.width = m_curPredictedInfo[3];
	objectPositionInfo.classification = static_cast<int>(this->m_classification);
	
	// if(m_historyTrajectoryInfo.historyTrajectory.size() > 40){
	// 	m_historyTrajectoryInfo.historyTrajectory.erase(m_historyTrajectoryInfo.historyTrajectory.begin());
	// }
	m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
	if (this->m_id == m_pConfigManager->m_debugID){
		int historySize = m_historyTrajectoryInfo.historyTrajectory.size(); 
		ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
		cout << "[kalmantracker positionSmooth 预测]"
			<< ", bottom.position[0] = " << bottom.position[0]
			<< ", bottom.position[1] = " << bottom.position[1]
			<<", bottomSpeedx = " << bottom.absoluteSpeedX
			<<", bottomSpeedy = " << bottom.absoluteSpeedY
			<< std::endl;
		m_pLogger->info("kalmantracker predict 存入历史轨迹 id1 = {}, statementPredicted(5) = {:.2f}", 
						this->m_id, statementPredicted(5) * 180 / M_PI);
	}
	positionSmooth(TrackingState::Predict);

	//if(m_historyTrajectoryInfo.predictedTrajectory.size() > 20){
	//	m_historyTrajectoryInfo.predictedTrajectory.erase(m_historyTrajectoryInfo.predictedTrajectory.begin());
	//}
	//m_historyTrajectoryInfo.predictedTrajectory.emplace_back(objectPositionInfo);//xyh//20220907 存储预测轨迹
	// cout<<"DEBUG: finished predict()\n";

	if (this->m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("kalmantracker predict 预测发布id = {},  statementPredicted(5) = {:.2f},  m_trackingBoxResult[5] = {:.2f} m_curPredictedInfo[5] = {:.2f}", 
						this->m_id, statementPredicted(5) * 180 / M_PI,  m_trackingBoxResult[5] * 180 / M_PI, m_curPredictedInfo[5]  * 180 / M_PI);
		int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
		if(historySize > 1){
			ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
			m_pLogger->debug("historySize yawDegree = {:.2f}, ", bottom.yawDegree);
		}
	}	

	return m_curPredictedInfo;
}

void ObjectTracking::update(const common_msgs::sensorobject& object, const double& timeStamp){
	if (this->m_id == m_pConfigManager->m_debugID)
		m_pLogger->info("kalmantracker update id = {}, value = {}", this->m_id, (int)object.value);
	
	//20221103 将状态量相关参数设置放到kalman更新前
	if((int)object.value == COMMON::SensorType::LIDAR || (int)object.value == COMMON::SensorType::OBU){//lidar only 设置6维观测量， 20221018 根据融合目标不同的速度来源选择不同的观测模型
		m_measureNum = m_measureNum_6;// x y w l h yaw
	}
	else if((int)object.value == COMMON::SensorType::RADAR || (int)object.value == COMMON::SensorType::LIDAR_RADAR
	        || (int)object.value == COMMON::SensorType::LIDAR_RADAR_OBU){//lidar-radar ,设置8维观测量
		// if(m_curPredictedInfo[6] > 2 * object.relspeedx || m_curPredictedInfo[6] < 2 * object.relspeedx
		//    || m_curPredictedInfo[7] > 2 * object.relspeedy || m_curPredictedInfo[7] < 2 * object.relspeedy){ //更改维度用于判断lidar-radar融合的速度是否异常
		// 	m_measureNum = m_measureNum_6;// x y w l h yaw
		// }
		// else{
			m_measureNum = m_measureNum_8;//x y w l h yaw vx vy (raw 6 x y w l h yaw)
		// }
	}
	else{//test
		m_measureNum = m_measureNum_8;//x y w l h yaw vx vy (raw 6 x y w l h yaw)
	}
	
	m_measurement = Eigen::VectorXd(m_measureNum,1);
	m_measurement.setZero();
	
	// 融合结果的维度：
	Eigen::MatrixXd H_in = Eigen::MatrixXd(m_measureNum, m_stateNum);//测量矩阵 H
	H_in.setIdentity();
	m_kalmanFilter.setH(H_in);
	
	// 测量噪声协方差; 测量误差 R 测量误差矩阵小，更信任测量值，所以以radar速度为准（预测值不使用，为0），，
	Eigen::MatrixXd R_in = Eigen::MatrixXd(m_measureNum,m_measureNum);
	R_in.setZero();
	R_in.diagonal().setConstant(0.1);//越小越相信测量
	
	R_in(0, 0) = 0.15;
	R_in(1, 1) = 0.15;
	R_in(2, 2) = 0.1;//w
	R_in(3, 3) = 0.1;//l
	R_in(4, 4) = 0.1;//h
	R_in(5, 5) = 0.05;
	
	// 20230220 当vx或vy的P值大于2时，降低vx或vy的R值，相信测量值
	Eigen::MatrixXd kalmanP = m_kalmanFilter.getP();
	
	if(m_measureNum == m_measureNum_8){
		//cout<<"kalmantracker update id = "<<this->m_id <<" P值过大，降低vx或vy的R值\n\t p66 = " << kalmanP(6, 6)
		//	<<", p77 = " << kalmanP(7, 7) << endl;
		//R_in(6, 6) = kalmanP(6, 6) > 1.0 ? 1 : 0.1;//vx
		//R_in(7, 7) = kalmanP(7, 7) > 1.0 ? 1 : 0.1;//vy
		
		R_in(6, 6) = 3;//vx
		R_in(7, 7) = 3;//vy
	}
	m_kalmanFilter.setR(R_in);
	
	//长宽修正
	float fixedLength = m_trackingBoxResult[2], fixedWidth = m_trackingBoxResult[3];
	setVelocityCorrectedBeforeUpdate(object);

	m_boxSize = {fixedLength, fixedWidth, object.height};
	float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading);
	float headingRadInNorthClockwise = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;

	setPositionCorrectedBeforeUpdate(object);

	// 静止目标不更新 TODO 传递时间戳，计算UTM速度
	int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
	if(historySize >= 3){
		ObjectPositionInfo lastTrackedInfo = m_historyTrajectoryInfo.historyTrajectory[historySize - 2];
		double deltaUTMx = abs(m_centerPointUTM[0] - lastTrackedInfo.position[0]); // 当前帧位置与上一帧位置的差值
		double deltaUTMy = abs(m_centerPointUTM[1] - lastTrackedInfo.position[1]);
		double UTMDistance = sqrt(deltaUTMx * deltaUTMx + deltaUTMy * deltaUTMy);
		// 此处m_absVelocityUTM与跟踪速度相同，如果m_absVelocityUTM与跟踪速度、UTM速度相差很大，同向取两者最小值（跟踪速度、UTM速度）,反向取绝对值最小的
		double fixedUTMSpeedVx = m_absVelocityUTM[0], fixedUTMSpeedVy = m_absVelocityUTM[1];
		float fixedUTMSpeed = sqrt(fixedUTMSpeedVx * fixedUTMSpeedVx + fixedUTMSpeedVy * fixedUTMSpeedVy);

		// // 检测约束:当前预测与上一帧跟踪误差大
		// float trackingSpeed = sqrt(m_trackingBoxResult[6] * m_trackingBoxResult[6] + m_trackingBoxResult[7] * m_trackingBoxResult[7]);
		// if(cos(headingRadInNorthClockwise - m_trackingBoxResult[5]) < 0.99 && trackingSpeed > 2){
		// 	headingRadInNorthClockwise = m_trackingBoxResult[5];
		// }


		if(UTMDistance < 0.1 && fixedUTMSpeed < 0.5){
			// 不更新
			if (this->m_id == m_pConfigManager->m_debugID)
				m_pLogger->info("kalmantracker update  historySize > 2 不更新 id = {}, UTMDistance = {:.2f}, fixedUTMSpeedVx = {:.2f}, fixedUTMSpeedVy = {:.2f}, fixedUTMSpeed = {:.2f}, 检测Vx = {:.2f}, 检测Vy = {:.2f}", 
							this->m_id, UTMDistance,fixedUTMSpeedVx,fixedUTMSpeedVy,fixedUTMSpeed, m_absVelocityUTM[0],m_absVelocityUTM[1]);
		}
		else{
			if (this->m_id == m_pConfigManager->m_debugID)
				m_pLogger->info("kalmantracker update  historySize > 2 更新 id = {}, m_measureNum = {}, UTMDistance = {:.2f}, fixedUTMSpeedVx = {:.2f}, fixedUTMSpeedVy = {:.2f}, fixedUTMSpeed = {:.2f}, 检测Vx = {:.2f}, 检测Vy = {:.2f}", 
						this->m_id, m_measureNum, UTMDistance,fixedUTMSpeedVx,fixedUTMSpeedVy,fixedUTMSpeed, m_absVelocityUTM[0],m_absVelocityUTM[1]);
			if(m_measureNum == m_measureNum_8){//x y w l h yaw vx vy
				m_measurement
						<< m_centerPointUTM[0], m_centerPointUTM[1], fixedLength, fixedWidth, object.height, headingRadInNorthClockwise, fixedUTMSpeedVx, fixedUTMSpeedVy;
			}
			else{//x y w l h yaw
				m_measurement <<  m_centerPointUTM[0], m_centerPointUTM[1], fixedLength, fixedWidth, object.height, headingRadInNorthClockwise;
			}

			// update
			m_kalmanFilter.kfUpdate(m_measurement);
			// NOTE 错误的匹配造成位置相差3米，造成速度更新误差大，与计算的fixedUTMSpeedVx，fixedUTMSpeedVy相差大
			Eigen::VectorXd statementUpdated = m_kalmanFilter.getX();
			if (this->m_id == m_pConfigManager->m_debugID){
				Eigen::MatrixXd kalmanP = m_kalmanFilter.getP();
				std::cout << FBLU("P trace: ") << m_kalmanFilter.getP().trace() << std::endl;
				std::cout << FBLU("P: ") <<", kalmanP(0,0) = " << kalmanP(0,0) 
					<<", kalmanP(1,1) = " << kalmanP(1,1)
					<<", kalmanP(6,6) = " << kalmanP(6,6)
					<<", kalmanP(7,7) = " << kalmanP(7,7)<< std::endl;
				m_pLogger->debug("kalmantracker 更新后update id = {}, statementUpdated[5] = {:.2f}, m_trackingBoxResult[5] = {:.2f}, 检测角度 = {:.2f}", 
						this->m_id, statementUpdated(5) * 180 / M_PI, m_trackingBoxResult[5] * 180.0 / M_PI, objectAngleDegreeInNorth_Clockwise);
				m_pLogger->debug("长宽信息：kalmantracker 更新后update：更新后长宽 id = {},跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
						this->m_id ,m_trackingBoxResult[2], m_trackingBoxResult[3]);
			}

			fixSpeedAfterUpdate(statementUpdated, object, fixedUTMSpeedVx,fixedUTMSpeedVy);

			if (this->m_id == m_pConfigManager->m_debugID){
				std::vector<double> objectSpeedInCarBackRFU = getSpeedInCarbackRFU();
				m_pLogger->debug("kalmantracker update速度约束后 id = {}, 横向速度 = {:.2f}, 纵向速度 = {:.2f}, m_trackingBoxResult[6] = {:.2f}, m_trackingBoxResult[7] = {:.2f}, objectSpeedInCarBackRFU vx = {:.2f}, objectSpeedInCarBackRFU vy = {:.2f}", 
						this->m_id, statementUpdated(6), statementUpdated(7), m_trackingBoxResult[6],m_trackingBoxResult[7],objectSpeedInCarBackRFU[0],objectSpeedInCarBackRFU[1]);
				m_pLogger->debug("kalmantracker update速度约束后 id = {}, 当前更新角度statementUpdated(5) = {:.2f}, 当前跟踪角度m_trackingBoxResult[5] = {:.2f}, objectAngleDegreeInNorth_Clockwise = {}", 
						this->m_id,	statementUpdated(5) * 180 / M_PI,m_trackingBoxResult[5] * 180 / M_PI, objectAngleDegreeInNorth_Clockwise);
				m_pLogger->debug("historySize yawDegree = {:.2f}, ", m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree);
			}

			// 更新航向角约束:当前预测与上一帧跟踪误差大
			std::vector<float> historyYawDegrees(historySize);
			for(int i = 0; i < historySize; i++){
				historyYawDegrees[i] = m_historyTrajectoryInfo.historyTrajectory[i].yawDegree;
			}

			if (this->m_id == m_pConfigManager->m_debugID){
				m_pLogger->debug("历史航向角");
				for(auto degree: historyYawDegrees){
					cout <<degree << ",";
				}
				cout << endl;
			}
			
			
			// // assert(historySize = historyYawDegrees.size());

			// double medianYawDegree = m_cCommon.caculateAverageAngle(historyYawDegrees);
			// auto it = m_historyTrajectoryInfo.historyTrajectory.rbegin();
			// for( ;it != m_historyTrajectoryInfo.historyTrajectory.rend(); ++it){
			// 	if((cos(medianYawDegree - it->yawDegree) * M_PI / 180.0) > 0.99){
			// 		// 
			// 		break;
			// 	}
			// }
			// if (this->m_id == m_pConfigManager->m_debugID){
			// 	m_pLogger->debug("计算角度平均值 id = {}, medianYawDegree = {:.2f}, it->yawDegree = {:.2f}", 
			// 			this->m_id,	medianYawDegree, it->yawDegree);
			// 	m_pLogger->debug("historySize yawDegree = {:.2f}, ", m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree);
			// }
			// medianYawDegree = it->yawDegree;

			// if(cos(statementUpdated(5) - m_trackingBoxResult[5]) < 0.99 && cos(statementUpdated(5) - it->yawDegree * M_PI / 180.0) < 0.99){
			// 	statementUpdated(5) = m_trackingBoxResult[5];
			// 	m_kalmanFilter.setHeading(m_trackingBoxResult[5]);
			// 	if (this->m_id == m_pConfigManager->m_debugID){
			// 		m_pLogger->debug("航向角修正: 更新后航向角修正 id = {}, 当前更新角度statementUpdated(5) = {:.2f}, 当前跟踪角度m_trackingBoxResult[5] = {:.2f}, it->yawDegree = {:.2f}", 
			// 				this->m_id,	statementUpdated(5) * 180 / M_PI,m_trackingBoxResult[5] * 180 / M_PI, it->yawDegree);
			// 		m_pLogger->debug("historySize yawDegree = {:.2f}, ", m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree);
			// 	}
			// }

			m_trackingBoxResult.clear();
			int statementUpdatedSize = statementUpdated.size();
			for(int i = 0; i < statementUpdatedSize; i++){
				m_trackingBoxResult.emplace_back(statementUpdated(i));
			}
			if (this->m_id == m_pConfigManager->m_debugID){
				std::vector<double> objectSpeedInCarBackRFU = getSpeedInCarbackRFU();
				m_pLogger->debug("kalmantracker headingSmooth前 id = {}, 横向速度 = {:.2f}, 纵向速度 = {:.2f}, m_trackingBoxResult[6] = {:.2f}, m_trackingBoxResult[7] = {:.2f}, objectSpeedInCarBackRFU vx = {:.2f}, objectSpeedInCarBackRFU vy = {:.2f}", 
						this->m_id, statementUpdated(6), statementUpdated(7), m_trackingBoxResult[6],m_trackingBoxResult[7],objectSpeedInCarBackRFU[0],objectSpeedInCarBackRFU[1]);
				m_pLogger->debug("kalmantracker headingSmooth前 id = {}, 当前更新角度statementUpdated(5) = {:.2f}, 当前跟踪角度m_trackingBoxResult[5] = {:.2f}, objectAngleDegreeInNorth_Clockwise = {}", 
						this->m_id,	statementUpdated(5) * 180 / M_PI,m_trackingBoxResult[5] * 180 / M_PI, objectAngleDegreeInNorth_Clockwise);
				m_pLogger->debug("historySize yawDegree = {:.2f}, ", m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree);
				m_pLogger->debug("长宽信息：headingSmooth前长宽 id = {},跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
						this->m_id ,m_trackingBoxResult[2], m_trackingBoxResult[3]);
				m_pLogger->debug("类别信息：headingSmooth前长宽 id = {}, 类别 = {}",
						this->m_id ,this->m_classification);
			}

			
		}


	}
	else{ //historySize < 3

		if (this->m_id == m_pConfigManager->m_debugID)
			cout << "\nkalmantracker update  historySize < 2 更新 id = " << this->m_id 
				<< ", m_measureNum = " << m_measureNum
				<< ", 检测Vx = " << m_absVelocityUTM[0] << ", 检测Vy = " << m_absVelocityUTM[1]<< std::endl;
		if(m_measureNum == m_measureNum_8){//x y w l h yaw vx vy
			m_measurement
					<< m_centerPointUTM[0], m_centerPointUTM[1], fixedLength, fixedWidth, object.height, headingRadInNorthClockwise, m_absVelocityUTM[0], m_absVelocityUTM[1];
		}
		else{//x y w l h yaw
			m_measurement <<  m_centerPointUTM[0], m_centerPointUTM[1], fixedLength, fixedWidth, object.height, headingRadInNorthClockwise;
		}
		
		// update
		m_kalmanFilter.kfUpdate(m_measurement);

		Eigen::VectorXd statementUpdated = m_kalmanFilter.getX();
		if(m_classification == COMMON::LidarDetectionClassification::Unknown
			|| object.classification == COMMON::LidarDetectionClassification::Unknown){
				velocitySmooth(statementUpdated); 
		}
		m_trackingBoxResult.clear();
		int statementUpdatedSize = statementUpdated.size();
		for(int i = 0; i < statementUpdatedSize; i++){
			m_trackingBoxResult.emplace_back(statementUpdated(i));
		}

		if (this->m_id == m_pConfigManager->m_debugID)
			cout << "kalmantracker update后 id = " << this->m_id
				<<", 横向速度 = " << statementUpdated(6)
				<<", 纵向速度 = " << statementUpdated(7)
				<<", 横向加速度 = " << statementUpdated(8)
				<<", 纵向加速度 = " << statementUpdated(9)
				<< std::endl;

	}
	
	m_time_since_update = 0;
	m_hits += 1;
	m_hit_streak += 1;
	m_objectOriginal = object;

	if(!m_cCommon.isMotorVehicle(this->m_classification)){
		this->m_max_age = 3;
	}

	if(m_cCommon.isMotorVehicle(this->m_classification)){
		headingSmooth(object, timeStamp);
	}
	else if(!m_cCommon.isMotorVehicle(this->m_classification) && this->m_classification != COMMON::LidarDetectionClassification::Unknown){
		nonMotorVehivleHeadingSmooth(object, timeStamp);
	}
	else{
		// TODO 针对聚类目标进行处理：速度小0，锁定航向角
		clusterObjectsHeadingSmooth(object, timeStamp);
	}
	
	
	if (this->m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("kalmantracker update headingSmooth后发布id = {},  m_trackingBoxResult[5] = {:.2f}", 
						this->m_id, m_trackingBoxResult[5] * 180 / M_PI);
		m_pLogger->debug("长宽信息： kalmantracker update headingSmooth后发布 id = {},跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
						this->m_id ,m_trackingBoxResult[2], m_trackingBoxResult[3]);
	}
	positionSmooth(TrackingState::Update);
	if (this->m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("kalmantracker update positionSmooth后发布id = {},  m_trackingBoxResult[5] = {:.2f}", 
						this->m_id, m_trackingBoxResult[5] * 180 / M_PI);
		int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
		if(historySize > 1){
			ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
			m_pLogger->debug("historySize yawDegree = {:.2f}, ", bottom.yawDegree);
		}
		
		m_pLogger->debug("长宽信息：kalmantracker update positionSmooth后发布 id = {},跟踪长 = {:.2f}, 跟踪宽 = {:.2f}",
						this->m_id ,m_trackingBoxResult[2], m_trackingBoxResult[3]);
		
	}
}

// 速度更新前，将错误的检测速度（radar相对速度+自车速度）修正
void ObjectTracking::setVelocityCorrectedBeforeUpdate(const common_msgs::sensorobject& object){
	if(object.value == COMMON::SensorType::LIDAR) {
		Eigen::MatrixXd Q = m_kalmanFilter.getQ();
		Q(6,6) = 1;
		Q(7,7) = 1;
		Q(8,8) = 1;
		Q(9,9) = 1;
		m_kalmanFilter.setQ(Q);
		m_absVelocityUTM = std::vector<double>{m_curPredictedInfo[6], m_curPredictedInfo[7], 0};//预测速度
		if (this->m_id == m_pConfigManager->m_debugID)
			cout << "\nsetVelocityCorrectedBeforeUpdate 更新LIDAR id = " << this->m_id << ", value1111 = " << this->m_value 
				<< ", RFU下 object.relspeedx = " << object.relspeedx << ", RFU下 object.relspeedy = " << object.relspeedy
				<< ", [预测速度vx] = " << m_curPredictedInfo[6] << ", [预测速度vy] = " << m_curPredictedInfo[7] 
				<< ", 自车速度vx = " << m_selfCarSpeed[0] << ", vy = " << m_selfCarSpeed[1] 
				<< ", 预测UTMVx m_absVelocityUTM = " << m_absVelocityUTM[0] << ", 预测UTMVy m_absVelocityUTM = " << m_absVelocityUTM[1]
				<< ", m_trackingBoxResult[6] = " << m_trackingBoxResult[6] 
				<< ", m_trackingBoxResult[7] = " << m_trackingBoxResult[7] 
				<< std::endl;
	}
	else if(object.value == COMMON::SensorType::LIDAR_RADAR){
		Eigen::Vector3d carBackRFUSpeed{object.relspeedx + m_selfCarSpeed[0], object.relspeedy + m_selfCarSpeed[1], 0};
		m_absVelocityUTM = m_sensorAxisTransformation.bodyAxis2ENU(carBackRFUSpeed);
		float trackingSpeed = sqrt(m_trackingBoxResult[6] * m_trackingBoxResult[6] + m_trackingBoxResult[7] * m_trackingBoxResult[7]);
		if (this->m_id == m_pConfigManager->m_debugID){
			cout << "\nsetVelocityCorrectedBeforeUpdate  更新LIDAR_RADAR id = " << this->m_id << ", value2222 = " << this->m_value 
			<< ", RFU下 object.relspeedx = " << object.relspeedx << ", RFU下 object.relspeedy = " << object.relspeedy
			<< ", 自车速度vx = " << m_selfCarSpeed[0] << ", vy = " << m_selfCarSpeed[1] 
			<< ", 检测RFU下绝对速度 carBackRFUSpeed[0] = " << carBackRFUSpeed[0] << ", carBackRFUSpeed[1] = " << carBackRFUSpeed[1] 
			<< ", 检测UTMVx m_absVelocityUTM= " << m_absVelocityUTM[0] << ", 检测UTMVy m_absVelocityUTM= " << m_absVelocityUTM[1]<< std::endl;
			m_pLogger->debug("速度更新前修正 id = {}, 预测跟踪横向速度 = {:.2f}, 预测跟踪纵向速度 = {:.2f}, trackingSpeed = {:.2f}",
						this->m_id ,m_trackingBoxResult[6], m_trackingBoxResult[7], trackingSpeed);
		}
		
		int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
		if(historySize >= 3 && m_hits > 6){
			if (this->m_id == m_pConfigManager->m_debugID){
				Eigen::Vector3d speedInUTM1{m_absVelocityUTM[0], m_absVelocityUTM[1], 0};
				std::vector<double> speedRFU1 = getSpeedInCarbackRFU(speedInUTM1);
				Eigen::Vector3d speedInUTM2{m_curPredictedInfo[6], m_curPredictedInfo[7], 0};
				std::vector<double> speedRFU2 = getSpeedInCarbackRFU(speedInUTM2);

				cout << "\nsetVelocityCorrectedBeforeUpdate 1 historySize = " << historySize << ", m_hits = " << m_hits 
					<< std::endl;
				cout << "\nsetVelocityCorrectedBeforeUpdate 1 speedRFU1 vx = " << speedRFU1[0] << ", vy = " << speedRFU1[1] 
					<< std::endl;
				cout << "\nsetVelocityCorrectedBeforeUpdate 1 speedRFU2 vx = " << speedRFU2[0] << ", vy = " << speedRFU2[1] 
					<< std::endl;
			}
			// 横向速度
			setAxisSpeed(&m_absVelocityUTM[0], m_curPredictedInfo[6], SPEED_THRESHOLD_SAME_DIRECTION, SPEED_THRESHOLD_OPPOSITE_DIRECTION,0);
			// 纵向速度
			setAxisSpeed(&m_absVelocityUTM[1], m_curPredictedInfo[7],SPEED_THRESHOLD_SAME_DIRECTION, SPEED_THRESHOLD_OPPOSITE_DIRECTION, 1);
		}
		else if(trackingSpeed > 2){
			if (this->m_id == m_pConfigManager->m_debugID){
				Eigen::Vector3d speedInUTM1{m_absVelocityUTM[0], m_absVelocityUTM[1], 0};
				std::vector<double> speedRFU1 = getSpeedInCarbackRFU(speedInUTM1);
				Eigen::Vector3d speedInUTM2{m_curPredictedInfo[6], m_curPredictedInfo[7], 0};
				std::vector<double> speedRFU2 = getSpeedInCarbackRFU(speedInUTM2);

				cout << "\nsetVelocityCorrectedBeforeUpdate 2 historySize = " << historySize << ", m_hits = " << m_hits 
					<< std::endl;
				cout << "\nsetVelocityCorrectedBeforeUpdate 2 speedRFU1 vx = " << speedRFU1[0] << ", vy = " << speedRFU1[1] 
					<< std::endl;
				cout << "\nsetVelocityCorrectedBeforeUpdate 2 speedRFU2 vx = " << speedRFU2[0] << ", vy = " << speedRFU2[1] 
					<< std::endl;
			}
			setAxisSpeed(&m_absVelocityUTM[0], m_trackingBoxResult[6], SPEED_THRESHOLD_SAME_DIRECTION, SPEED_THRESHOLD_OPPOSITE_DIRECTION,0);
			setAxisSpeed(&m_absVelocityUTM[1], m_trackingBoxResult[7],SPEED_THRESHOLD_SAME_DIRECTION, SPEED_THRESHOLD_OPPOSITE_DIRECTION, 1);
		}
		else{
			// 遍历历史轨迹速度（已删），横纵向速度角度超出阈值，认为radar速度不正常
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "\nsetVelocityCorrectedBeforeUpdate 速度优化-vx异号 小于等于1m/s，认为radar速度正常 id = " << this->m_id 
					<< ", historySize = " << historySize << ", m_hits = " << m_hits 
					<< std::endl;
		}
	}//lidar-radar
	else{
		Eigen::Vector3d carBackRFUSpeed{object.relspeedx + m_selfCarSpeed[0], object.relspeedy + m_selfCarSpeed[1], 0};
		m_absVelocityUTM = m_sensorAxisTransformation.bodyAxis2ENU(carBackRFUSpeed);
		if (this->m_id == m_pConfigManager->m_debugID)
			cout << "\nsetVelocityCorrectedBeforeUpdate update  更新other id = " << this->m_id << ", value3333 = " << this->m_value 
				<< ", RFU下 object.relspeedx = " << object.relspeedx << ", RFU下 object.relspeedy = " << object.relspeedy
				<< ", RFU下绝对速度 carBackRFUSpeed[0] = " << carBackRFUSpeed[0] << ", carBackRFUSpeed[1] = " << carBackRFUSpeed[1] 
				<< ", 检测UTMVx m_absVelocityUTM=" << m_absVelocityUTM[0] << ", 检测UTMVym_absVelocityUTM = " << m_absVelocityUTM[1]<< std::endl;
	}
}

void ObjectTracking::setAxisSpeed(double* absVelocityUTM_Axis, double curPredictedInfo_Axis, 
	 const double& sameDirectionThreshold, const double& oppositeDirectionThreshold,const int& velocityIndex){
	// 判断是否同向
	bool sameDirection = (*absVelocityUTM_Axis) * curPredictedInfo_Axis > 0;
	double threshold = sameDirection ? sameDirectionThreshold : oppositeDirectionThreshold;

	// 判断并更新速度： 
	if((sameDirection && std::abs(*absVelocityUTM_Axis - curPredictedInfo_Axis) > threshold )|| 
		(!sameDirection && (std::abs(*absVelocityUTM_Axis) + std::abs(curPredictedInfo_Axis)) > threshold)) {
		// 如果同号，误差大于3m/s，认为radar速度异常,使用预测速度
		// 如果异号，误差大于1m/s，认为radar速度异常,使用预测速度
		if (this->m_id == m_pConfigManager->m_debugID)
				cout << "\nsetAxisSpeed 速度优化-认为radar速度异常,使用预测速度 id = " << this->m_id 
					<<", velocityIndex(1纵向) = " << velocityIndex
					<< ", 同号标志sameDirection = " << sameDirection << ", 速度阈值threshold = " << threshold
					<< ", 检测UTMVx m_absVelocityUTM= " << *absVelocityUTM_Axis 
					<< ", 预测 m_curPredictedInfo[0] = " << curPredictedInfo_Axis<< std::endl;
		*absVelocityUTM_Axis = curPredictedInfo_Axis;
	}
	else{
		// 原固定阈值替换为动态阈值计算
		const double base_threshold = 0.5; // 基础阈值0.5m/s
		const double speed_factor = 0.3;   // 速度比例系数

		// 动态计算阈值（速度越大阈值越大）
		double dynamic_threshold = base_threshold + speed_factor * std::abs(curPredictedInfo_Axis);

		// 同向判断条件
		bool speed_abnormal = false;
		if(sameDirection) {
			// 同向时使用动态阈值
			speed_abnormal = std::abs(*absVelocityUTM_Axis - curPredictedInfo_Axis) > dynamic_threshold;
		} else {
			// 反向时使用固定比例阈值（原逻辑保持）
			const double opposite_threshold = 1.0; // 反向基础阈值1m/s
			speed_abnormal = (std::abs(*absVelocityUTM_Axis) + std::abs(curPredictedInfo_Axis)) > opposite_threshold;
		}

		if(speed_abnormal) {
			if (this->m_id == m_pConfigManager->m_debugID) {
				cout << "\nsetAxisSpeed 动态阈值[ " << dynamic_threshold 
					<< " ] 速度差异[ " << std::abs(*absVelocityUTM_Axis - curPredictedInfo_Axis)
					<< " ] 预测速度[ " << curPredictedInfo_Axis << " ]"
					<<", velocityIndex(1纵向) = " << velocityIndex << endl;
			}
			*absVelocityUTM_Axis = curPredictedInfo_Axis;
		}
		
		if (this->m_id == m_pConfigManager->m_debugID){
			cout << "\nsetAxisSpeed 速度优化-其他情况，需要再判断，使用检测速度 id = " << this->m_id 
				<<", velocityIndex(1纵向) = " << velocityIndex
				<<", sameDirection(1同号) = " << sameDirection
				<<", 同号差值：" << std::abs(*absVelocityUTM_Axis - curPredictedInfo_Axis)
				<<", 异号差值：" << std::abs(*absVelocityUTM_Axis) + std::abs(curPredictedInfo_Axis)
				<<", threshold = " << threshold
				<< ", 检测速度UTM m_absVelocityUTM= " << *absVelocityUTM_Axis 
				<< ", 预测 m_curPredictedInfo = " << curPredictedInfo_Axis << std::endl;
		}
	}
}

// 位置更新前，将错误的检测位置修正
void ObjectTracking::setPositionCorrectedBeforeUpdate(const common_msgs::sensorobject& object){
	// TODO 速度同向反向处理
	if(m_classification != COMMON::LidarDetectionClassification::Unknown 
		&& object.classification == COMMON::LidarDetectionClassification::Unknown)
	{
		// 匹配到聚类目标，目标速度低，使用上一帧位置，目标速度大，使用聚类位置（计算速度与跟踪速度反向，使用上一帧位置，否则使用聚类位置）
		Eigen::Vector3d clusterENUPosition{object.x, object.y,object.z};
		std::vector<double> clusterCenterUTM = m_sensorAxisTransformation.ENU2UTM(clusterENUPosition);

		double deltaUTMx = abs(m_centerPointUTM[0] - clusterCenterUTM[0]); // 当前帧检测位置与上一帧跟踪位置的差值
		double deltaUTMy = abs(m_centerPointUTM[1] - clusterCenterUTM[1]);
		double UTMDistance = sqrt(deltaUTMx * deltaUTMx + deltaUTMy * deltaUTMy);

		if(UTMDistance > 1){
			m_centerPointUTM =std::vector<double>{m_trackingBoxResult[0], m_trackingBoxResult[1], object.z};
			if (this->m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("kalmantracker 更新前修正位置 更新聚类位置-UTMDistance > 1,使用上一帧跟踪位置 id = {}, UTMDistance = {.2f}",
								this->m_id, UTMDistance);
		}
		else{
			// 正常目标使用检测位置
			Eigen::Vector3d ENUPosition{object.x, object.y,object.z};
			m_centerPointUTM = m_sensorAxisTransformation.ENU2UTM(ENUPosition);
			if (this->m_id == m_pConfigManager->m_debugID)
				m_pLogger->debug("kalmantracker 更新前修正位置 更新聚类位置-else id = {}, UTMDistance = {.2f}",
								this->m_id, UTMDistance);
		}
	}
	else{
		// 正常目标使用检测位置
		Eigen::Vector3d ENUPosition{object.x, object.y,object.z};
		m_centerPointUTM = m_sensorAxisTransformation.ENU2UTM(ENUPosition);
	}
}


void ObjectTracking::fixSpeedAfterUpdate(Eigen::VectorXd& statementUpdated, const common_msgs::sensorobject& object, const double& fixedUTMSpeedVx,const double& fixedUTMSpeedVy){
	Eigen::Vector3d speedInUTM1{m_trackingBoxResult[6],m_trackingBoxResult[7], 0};
	std::vector<double> objectSpeedInCarBackRFU1 = getSpeedInCarbackRFU(speedInUTM1);
	float speedRad1 = atan2(m_trackingBoxResult[6],m_trackingBoxResult[7]);
	speedRad1 = m_cCommon.normalizeAngle(speedRad1);
	Eigen::Vector3d speedInUTM2{statementUpdated(6), statementUpdated(7), 0};
	std::vector<double> objectSpeedInCarBackRFU2 = getSpeedInCarbackRFU(speedInUTM2);
	float speedRad2 = atan2(statementUpdated(6), statementUpdated(7));
	speedRad2 = m_cCommon.normalizeAngle(speedRad2);
	float speedDegreeGap = (speedRad2-speedRad1) * 180 / M_PI;
	speedDegreeGap = m_cCommon.normalizeAngle(speedDegreeGap);

	if (this->m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("kalmantracker update id = {}, 预测m_trackingBoxResult[6] = {:.2f}, m_trackingBoxResult[7] = {:.2f}, 预测objectSpeedInCarBackRFU vx = {:.2f}, 预测objectSpeedInCarBackRFU vy = {:.2f}, speedDegree1 = {:.2f}", 
				this->m_id, m_trackingBoxResult[6],m_trackingBoxResult[7],objectSpeedInCarBackRFU1[0],objectSpeedInCarBackRFU1[1], speedRad1 * 180 / M_PI);
		
		m_pLogger->debug("kalmantracker update id = {}, 更新后statementUpdated(6) = {:.2f}, statementUpdated(7) = {:.2f}, 更新objectSpeedInCarBackRFU vx = {:.2f}, 更新objectSpeedInCarBackRFU vy = {:.2f}, speedDegree2 = {:.2f}", 
				this->m_id, statementUpdated(6),statementUpdated(7),objectSpeedInCarBackRFU2[0],objectSpeedInCarBackRFU2[1], speedRad2 * 180 / M_PI);
		m_pLogger->debug("kalmantracker update id = {}, speedDegreeGap = {:.2f}", 
				this->m_id, speedDegreeGap);
	}

	if(m_classification == COMMON::LidarDetectionClassification::Unknown
		|| object.classification == COMMON::LidarDetectionClassification::Unknown){
			velocitySmooth(statementUpdated); 
	}

	float fixSpeedUTM = sqrt(fixedUTMSpeedVx * fixedUTMSpeedVx + fixedUTMSpeedVy * fixedUTMSpeedVy);
	if(abs(speedDegreeGap) > 6 && fixSpeedUTM > 2){ // 当前测试数据最大5.37，预测横纵向速度与更新横纵向速度对比的角度差过大，取平均速度
		Eigen::Vector3d speedInUTMAfterSmooth{m_trackingBoxResult[6],m_trackingBoxResult[7], 0};
		std::vector<double> objectSpeedInCarBackRFUAfterSmooth = getSpeedInCarbackRFU(speedInUTMAfterSmooth);

		if (this->m_id == m_pConfigManager->m_debugID)
			m_pLogger->debug("update vx速度异常限制：更新后长宽 id = {},fixedUTMSpeedVx = {:.2f}, statementUpdated(6) = {:.2f}, m_trackingBoxResult取值 = {:.2f}, objectSpeedInCarBackRFUVx = {:.2f}",
				this->m_id ,fixedUTMSpeedVx, statementUpdated(6),m_trackingBoxResult[6],objectSpeedInCarBackRFUAfterSmooth[0]);
		statementUpdated(6) = m_trackingBoxResult[6];
		m_kalmanFilter.setVx(m_trackingBoxResult[6]);

		if (this->m_id == m_pConfigManager->m_debugID)
			m_pLogger->debug("update vy速度异常限制：更新后长宽 id = {},fixedUTMSpeedVy = {:.2f}, statementUpdated(7) = {:.2f}, m_trackingBoxResult取值 = {:.2f}, objectSpeedInCarBackRFUVx = {:.2f}",
				this->m_id ,fixedUTMSpeedVy, statementUpdated(7),m_trackingBoxResult[7],objectSpeedInCarBackRFUAfterSmooth[1]);
		statementUpdated(7) = m_trackingBoxResult[7];
		m_kalmanFilter.setVy(statementUpdated(7));
	}

}

void ObjectTracking::headingSmooth(const common_msgs::sensorobject& object, const double& timeStamp){
	Eigen::VectorXd statementUpdated = m_kalmanFilter.getX();

	bool is_update_history = false;

	//x y w l h yaw vx vy
	std::vector<float> updatedStateInfo = {statementUpdated(0), statementUpdated(1), statementUpdated(2), statementUpdated(3), statementUpdated(4), statementUpdated(5),statementUpdated(6), statementUpdated(7)};
	ObjectPositionInfo objectPositionInfo = packageObjectPositionInfo(updatedStateInfo,
																		timeStamp,
																		object);
	
	// 基于距离保留轨迹，最多40帧，最大10米
	ObjectPositionInfo front_traj = m_historyTrajectoryInfo.historyTrajectory[0];
	double delta_x_front = front_traj.position[0] - objectPositionInfo.position[0];
	double delta_y_front = front_traj.position[1] - objectPositionInfo.position[1];
	double distance_front = sqrt(pow(delta_x_front, 2) + pow(delta_y_front, 2));
	float timeGap = abs(front_traj.timeStamp - objectPositionInfo.timeStamp);
	float objectUpdatedSpeed = sqrt(updatedStateInfo[6] * updatedStateInfo[6] + updatedStateInfo[7] * updatedStateInfo[7]);
	
	if(!m_historyTrajectoryInfo.historyTrajectory.empty()){
		m_historyTrajectoryInfo.historyTrajectory.pop_back();
	}

	if (timeGap != 0 && ((m_historyTrajectoryInfo.historyTrajectory.size() > 20) || (distance_front > 10 && m_historyTrajectoryInfo.historyTrajectory.size() > 1))) // 10.0
	{
		m_historyTrajectoryInfo.historyTrajectory.erase(m_historyTrajectoryInfo.historyTrajectory.begin());
	}

	if (m_historyTrajectoryInfo.historyTrajectory.size() < 1)
	{
		if (this->m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("存入历史轨迹航向角：{:.2f}", objectPositionInfo.yawDegree);
		}
		
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
		is_update_history = true;
	}
	else
	{
		if (this->m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("存入历史轨迹航向角：{:.2f}", objectPositionInfo.yawDegree);
		}
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
		is_update_history = true;
	}


	//历史轨迹修正航向角：计算kalman更新当前帧航向角后使用历史轨迹修正航向角
	int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
	std::vector<float> historyYawDegrees(historySize);
	for(int i = 0; i < historySize; i++){
		historyYawDegrees[i] = m_historyTrajectoryInfo.historyTrajectory[i].yawDegree;
	}

	if (this->m_id == m_pConfigManager->m_debugID){
		m_pLogger->debug("id = {}, historySize = {}, timeGap = {:.2f}, 历史航向角：",
			this->m_id, historySize, timeGap);
		std::string degreeStr = "";
		for(auto degree: historyYawDegrees){
			degreeStr += std::to_string(degree) + ",";
		}
		m_pLogger->debug("历史航向角：\n \t{}",degreeStr);
	}

	if(m_classSwitchType == ClassSwitchType::Bicycle2Car || m_classSwitchType == ClassSwitchType::Cone2Car || m_classSwitchType == ClassSwitchType::Unknown2Car){
		// 一般出现在目标开始出现在lidar扫描到的前几帧，此时不稳定，不能使用最大类别的航向角或航向角的均值
		double curDetectedHeadingDegree = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading); 
		if (this->m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("类别跳变更新跟踪航向角, 原始跟踪角度 = {:.2f}, curDetectedHeadingDegree = {:.2f}", 
					m_trackingBoxResult[5]* 180 / M_PI,curDetectedHeadingDegree);
		}

		if(is_update_history){
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = curDetectedHeadingDegree;
		}
		// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
		m_trackingBoxResult[5] = curDetectedHeadingDegree * M_PI / 180.0;

		// 之前的历史航向角都是错的，删除历史轨迹，只保留最后一个数据
		if (!m_historyTrajectoryInfo.historyTrajectory.empty()) {
			m_historyTrajectoryInfo.historyTrajectory.erase(
				m_historyTrajectoryInfo.historyTrajectory.begin(),
				m_historyTrajectoryInfo.historyTrajectory.end() - 1
			);
		}
		m_isHeadingInit = true; // 设置航向角初始化完成，不用删除历史轨迹了
		return;
	}

	// 历史数据有值
	if (historySize >= 5){
		// 最后一帧的历史航向角
		if(m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree < 0){
			double angleRad = m_cCommon.normalizeAngle(m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree * M_PI / 180.0);
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = angleRad * 180.0 / M_PI;
		}

		if(object.classification == COMMON::LidarDetectionClassification::Unknown){
			// 匹配到聚类结果使用上一帧跟踪角度
			if (this->m_id == m_pConfigManager->m_debugID)
			{
				cout << "[kalmantracker update] 10 匹配到聚类结果 使用上一帧角度"
					<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
					<< ", [上一帧跟踪角度] = " << m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree
					<< std::endl;
			}
			if(is_update_history){
				m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
					= m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree;
			}
			// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			m_trackingBoxResult[5] = m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree * M_PI / 180.0;
			// m_kalmanFilter.setHeading(m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree * M_PI / 180.0);
			// m_measurement(5) = m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree * M_PI / 180.0;
			return;
		}
		
		
		// 历史帧数大于阈值才计算轨迹航向角
		ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];

		float objectAngleDegreeInNorth_Clockwise = bottom.yawDegree;
		ObjectPositionInfo objectInfoToCaculate;
		float caculateSpeed = sqrt(bottom.absoluteSpeedX * bottom.absoluteSpeedX + bottom.absoluteSpeedY * bottom.absoluteSpeedY);;
		bool isCaculateHeadingSuccess = false; //是否使用计算航向角
		int objectInfoToCaculateIndex = historySize - 1;
		float caculateDistance = 0;

		for(int i = historySize - 2; i >= 0; i--){

			ObjectPositionInfo penultimate = m_historyTrajectoryInfo.historyTrajectory[i];
			double deltaX = bottom.position[0] - penultimate.position[0];
			double deltaY = bottom.position[1] - penultimate.position[1];
			double distance = sqrt(pow(deltaX, 2) + pow(deltaY, 2));
			double deltaT = bottom.timeStamp - penultimate.timeStamp;
			
			if(m_cCommon.isMotorVehicle(this->m_classification) && distance < 0.5)
				continue;
			
			objectAngleDegreeInNorth_Clockwise = atan2(deltaX, deltaY + 1e-6) * 180 / M_PI;
			if (objectAngleDegreeInNorth_Clockwise < 0){
				objectAngleDegreeInNorth_Clockwise += 360.0;
			}
			if(objectAngleDegreeInNorth_Clockwise >= 360){
				objectAngleDegreeInNorth_Clockwise -= 360.0;
			}

			if (this->m_id == m_pConfigManager->m_debugID)
			{
				float objectAngleDegreeInCarBackRFU_Clockwise = 
					m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
	
				cout
					<< "[kalmantracker update] 计算历史航向角，distance = " 
					<< distance 
					<< ", deltaX = " << deltaX
					<< ", deltaY = " << deltaY
					<< ", deltaT = " << deltaT
					<< ", 轨迹航向角（north）= " << objectAngleDegreeInNorth_Clockwise
					<< ", gps heading = " << m_selfCarGPS.heading
					<< ", bottom.yawDegree = " << bottom.yawDegree
					<< ", penultimate.yawDegree = " << penultimate.yawDegree
					<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
					<< ", 第一帧航向角 = " << m_historyTrajectoryInfo.historyTrajectory[0].yawDegree
					<< std::endl;
			}
			objectInfoToCaculate = m_historyTrajectoryInfo.historyTrajectory[i];
			objectInfoToCaculateIndex = i;
			caculateSpeed = distance / (deltaT + 1e-6);
			caculateDistance = distance;
			isCaculateHeadingSuccess = true;
			break;
		}

		double last_yaw_degree = m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree;
		double lastThirddegree = m_historyTrajectoryInfo.historyTrajectory[historySize - 3].yawDegree;
		// curDetectedHeadingDegree是用来更新m_trackingBoxResult[5]的，不能用来比较
		double curDetectedHeadingDegree = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading); 
		double curReverseHeadingDegree = curDetectedHeadingDegree + 180.0;
		if(curReverseHeadingDegree >= 360.0)
			curReverseHeadingDegree -= 360.0;
		double reverseTrackingRad = m_trackingBoxResult[5] + M_PI;
		if(reverseTrackingRad >= 2.0 * M_PI)
			reverseTrackingRad -= 2.0 * M_PI;


		double medianYawDegree = m_cCommon.getTrimmedMean(historyYawDegrees, 0.1);
		double averageYawDegree = m_cCommon.caculateAverageAngle(historyYawDegrees);
		auto it = m_historyTrajectoryInfo.historyTrajectory.rbegin();
		if (this->m_id == m_pConfigManager->m_debugID)
		{
			cout << "[kalmantracker update] 1 计算中值角度"
				<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
				<< ", medianYawDegree = " << medianYawDegree
				<< ", averageYawDegree = " << averageYawDegree
				<< std::endl;
		}
		for( ;it != m_historyTrajectoryInfo.historyTrajectory.rend(); ++it){
			if((cos(averageYawDegree - it->yawDegree) * M_PI / 180.0) > 0.99){
				averageYawDegree = it->yawDegree;
				break;
			}
		}

		float minSpeed = min(caculateSpeed, objectUpdatedSpeed);
		if (this->m_id == m_pConfigManager->m_debugID)
		{
			cout << "[kalmantracker update] 计算角度"
				<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
				<< ", last_yaw_degree = " << last_yaw_degree
				<< ", lastThirddegree = " << lastThirddegree
				<< ", curDetectedHeadingDegree = " << curDetectedHeadingDegree
				<< ", curReverseHeadingDegree = " << curReverseHeadingDegree
				<< "，计算航向角（north）= " << objectAngleDegreeInNorth_Clockwise
				<< ", 反向跟踪reverseTrackingRad = " << reverseTrackingRad * 180 / M_PI
				<< ", medianYawDegree = " << medianYawDegree
				<< ", averageYawDegree = " << averageYawDegree
				<<", isCaculateHeadingSuccess = " << isCaculateHeadingSuccess
				<< std::endl;
		}

		if (cos(last_yaw_degree * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.99
			&& cos(m_trackingBoxResult[5] - (objectAngleDegreeInNorth_Clockwise * M_PI / 180.0)) >  0.99
			){
				// 找跟检测角度最近的角度
				std::vector<float> yawDegreeList{last_yaw_degree, m_trackingBoxResult[5] * 180.0 / M_PI, objectAngleDegreeInNorth_Clockwise};
				
				float maxYawDegreeDistance = cos((yawDegreeList[0] - curDetectedHeadingDegree)  * M_PI / 180.0);
				float maxYawDegreeIndex = 0;

				for(size_t i = 1; i < yawDegreeList.size(); ++i){
					float curDegreeDistance = cos((yawDegreeList[i] - curDetectedHeadingDegree)  * M_PI / 180.0);
					if(curDegreeDistance > maxYawDegreeDistance){
						maxYawDegreeDistance = curDegreeDistance;
						maxYawDegreeIndex = i;
					}
				}

				float finalDegree = yawDegreeList[maxYawDegreeIndex];
				// 当前跟踪角度与上一帧跟踪角度和计算航向角误差小，不处理
				if (this->m_id == m_pConfigManager->m_debugID)
				{
					float objectAngleDegreeInCarBackRFU_Clockwise = 
							m_sensorAxisTransformation.NorthClockwise2CarBackRFU(curReverseHeadingDegree, m_selfCarGPS.heading);
					cout << "[kalmantracker update] 0 三个角度一致，使用final"
						<< "\n[当前跟踪角度] = " << m_trackingBoxResult[5] * 180 / M_PI
						<< ", 上一帧跟踪角度 = " << last_yaw_degree
						<< ", ，计算航向角（north） = " << objectAngleDegreeInNorth_Clockwise
						<< ", reverse检测角度 = " << curReverseHeadingDegree
						<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
						<< ", minSpeed = " << minSpeed
						<< ", finalDegree = " << finalDegree
						<< std::endl;
				}
				if(is_update_history){
					m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
						= finalDegree;
				}
				// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
				m_trackingBoxResult[5] = finalDegree * M_PI / 180.0;
		}
		else if(cos(curReverseHeadingDegree * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.99){
			if (this->m_id == m_pConfigManager->m_debugID)
			{
				float objectAngleDegreeInCarBackRFU_Clockwise = 
						m_sensorAxisTransformation.NorthClockwise2CarBackRFU(curReverseHeadingDegree, m_selfCarGPS.heading);
				cout << "[kalmantracker update] 2 使用反向检测角度"
					<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
					<< ", 上一帧跟踪角度 = " << last_yaw_degree
					<< ", ，计算航向角（north） = " << objectAngleDegreeInNorth_Clockwise
					<< ", [reverse跟踪角度] = " << curReverseHeadingDegree
					<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
					<< ", minSpeed = " << minSpeed
					<< std::endl;
			}
			if(is_update_history){
				m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
					= curReverseHeadingDegree;
			}
			// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			m_trackingBoxResult[5] = curReverseHeadingDegree * M_PI / 180.0;
		}
		else if(isCaculateHeadingSuccess && cos(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.6
			&& cos(m_trackingBoxResult[5] - averageYawDegree * M_PI / 180.0) > 0.6
		// 41.4度范围内
		){
			// 找跟检测角度最近的角度
			std::vector<float> yawDegreeList{last_yaw_degree, m_trackingBoxResult[5] * 180.0 / M_PI, objectAngleDegreeInNorth_Clockwise};
			
			float maxYawDegreeDistance = cos((yawDegreeList[0] - curDetectedHeadingDegree)  * M_PI / 180.0);
			float maxYawDegreeIndex = 0;

			for(size_t i = 1; i < yawDegreeList.size(); ++i){
				float curDegreeDistance = cos((yawDegreeList[i] - curDetectedHeadingDegree)  * M_PI / 180.0);
				if(curDegreeDistance > maxYawDegreeDistance){
					maxYawDegreeDistance = curDegreeDistance;
					maxYawDegreeIndex = i;
				}
			}

			float finalDegree = yawDegreeList[maxYawDegreeIndex];

			if (this->m_id == m_pConfigManager->m_debugID)
			{
				float objectAngleDegreeInCarBackRFU_Clockwise = 
						m_sensorAxisTransformation.NorthClockwise2CarBackRFU(last_yaw_degree, m_selfCarGPS.heading);
				cout << "[kalmantracker update] 5 使用跟踪角度-------------- final check"
					<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
					<< ", 上一帧跟踪角度 = " << last_yaw_degree
					<< ", ，计算航向角（north） = " << objectAngleDegreeInNorth_Clockwise
					<< ", reverse跟踪角度 = " << curReverseHeadingDegree
					<< ", minSpeed = " << minSpeed
					<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
					<< ", minSpeed = " << minSpeed
					<< ", [finalDegree] = " << finalDegree
					<< std::endl;
				m_pLogger->debug("5 使用跟踪角度--------------check, 角度余弦值 = {:.2f}, 确认余弦值 = {:.2f}, 过阈值 = {}, 跟踪 = {:.2f}, median  = {:.2f}", 
								cos(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0 - m_trackingBoxResult[5]),
								cos(m_trackingBoxResult[5] - averageYawDegree * M_PI / 180.0),
								cos(m_trackingBoxResult[5] - averageYawDegree * M_PI / 180.0) > 0.6,
								m_trackingBoxResult[5] * 180.0 / M_PI,
								averageYawDegree);
			}
			if(is_update_history){
				m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
					= finalDegree;
			}
			// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			m_trackingBoxResult[5] = finalDegree * M_PI / 180.0;
				
		}
		else{
			
				std::vector<float> yawDegreeList{last_yaw_degree, reverseTrackingRad * 180.0 / M_PI, curReverseHeadingDegree};
				
				float maxYawDegreeDistance = cos((yawDegreeList[0] - averageYawDegree)  * M_PI / 180.0);
				float maxYawDegreeIndex = 0;

				for(size_t i = 1; i < yawDegreeList.size(); ++i){
					float curDegreeDistance = cos((yawDegreeList[i] - averageYawDegree)  * M_PI / 180.0);
					if(curDegreeDistance > maxYawDegreeDistance){
						maxYawDegreeDistance = curDegreeDistance;
						maxYawDegreeIndex = i;
					}
				}

				float finalDegree = yawDegreeList[maxYawDegreeIndex];

				if(minSpeed > 2 && cos(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.95){
					if (this->m_id == m_pConfigManager->m_debugID){
						m_pLogger->debug("6 使用计算角度,finalDegree = {:.2f}, 跟踪角度 = {:.2f}, [objectAngleDegreeInNorth_Clockwise] = {:.2f}", 
								finalDegree,m_trackingBoxResult[5]* 180 / M_PI,objectAngleDegreeInNorth_Clockwise);
					}
					finalDegree = objectAngleDegreeInNorth_Clockwise;
				}
				else if(cos((last_yaw_degree - averageYawDegree)* M_PI / 180.0) > 0.95 
						&& cos((curReverseHeadingDegree - averageYawDegree) * M_PI / 180.0) > 0.95
						&& cos((curReverseHeadingDegree - last_yaw_degree) * M_PI / 180.0) > 0.99){
					if (this->m_id == m_pConfigManager->m_debugID){
						m_pLogger->debug("7 使用反向检测角度,finalDegree = {:.2f}, 跟踪角度 = {:.2f}, last_yaw_degree = {:.2f}, averageYawDegree = {:.2f}, [curReverseHeadingDegree] = {:.2f}, objectAngleDegreeInNorth_Clockwise = {:.2f}", 
								finalDegree,m_trackingBoxResult[5]* 180 / M_PI,last_yaw_degree, averageYawDegree, curReverseHeadingDegree, objectAngleDegreeInNorth_Clockwise);
					}
					finalDegree = curReverseHeadingDegree;
				}

				if (this->m_id == m_pConfigManager->m_debugID)
				{
					float objectAngleDegreeInCarBackRFU_Clockwise = 
							m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
					cout << "kalmantracker update 10 使用final角度------------------------check"
						<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
						<< ", [last_yaw_degree] = " << last_yaw_degree
						<< ", ，计算航向角（north） = " << objectAngleDegreeInNorth_Clockwise
						<< ", curDetectedHeadingDegree = " << curDetectedHeadingDegree
						<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
						<< ", 上一帧跟踪角度 = " << last_yaw_degree
						<< ", reverse检测角度 = " << curReverseHeadingDegree
						<< ", reverseTrackingRad = " << reverseTrackingRad * 180 / M_PI
						<< ", finalDegree = " << finalDegree
						<< ", averageYawDegree = " << averageYawDegree
						<< ", minSpeed = " << minSpeed
						<< std::endl;
				}
				if(is_update_history){
						m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
							= finalDegree;
					}
				// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
				m_trackingBoxResult[5] = finalDegree * M_PI / 180.0;
		}

	}
	else{ // historySize < 4
		// if(object.classification == COMMON::LidarDetectionClassification::Unknown){
		// 	// 匹配到聚类结果使用上一帧跟踪角度
		// 	if (this->m_id == m_pConfigManager->m_debugID)
		// 	{
		// 		cout << "[kalmantracker update] 10 匹配到聚类结果 使用上一帧角度"
		// 			<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
		// 			<< ", [上一帧跟踪角度] = " << m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree
		// 			<< std::endl;
		// 	}
		// 	if(is_update_history){
		// 		m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
		// 			= m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree;
		// 	}
		// 	// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
		// 	m_trackingBoxResult[5] = m_historyTrajectoryInfo.historyTrajectory[historySize - 2].yawDegree * M_PI / 180.0;
		// 	return;
		// }

		// 不是匹配到聚类结果
		// double firstHeadingDegreeInNorth = m_historyTrajectoryInfo.historyTrajectory[0].yawDegree;
		// if (this->m_id == m_pConfigManager->m_debugID)
		// {
		// 	float objectAngleDegreeInCarBackRFU_Clockwise = 
		// 			m_sensorAxisTransformation.NorthClockwise2CarBackRFU(firstHeadingDegreeInNorth, m_selfCarGPS.heading);
		// 	cout << "[kalmantracker update] 0 使用跟踪角度"
		// 		<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
		// 		<< ", [第一帧跟踪角度] = " << firstHeadingDegreeInNorth
		// 		<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
		// 		<< ", objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
		// 		<< ", gps heading = " << m_selfCarGPS.heading
		// 		<< std::endl;
		// }
		// if(is_update_history){
		// 	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
		// 		= firstHeadingDegreeInNorth;
		// }
		// // 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
		// m_trackingBoxResult[5] = firstHeadingDegreeInNorth * M_PI / 180.0;
	}
	// 前四帧不存储历史信息
	if(is_update_history && !m_isHeadingInit){
		if (this->m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("删除前4帧数据, m_historyCount = {}, history size = {}", m_historyCount, m_historyTrajectoryInfo.historyTrajectory.size());
		}
		m_historyTrajectoryInfo.historyTrajectory.erase(m_historyTrajectoryInfo.historyTrajectory.begin());
		++m_historyCount;

		if(m_historyCount >= 5){
			m_isHeadingInit = true;
		}
	}
	else{
		if (this->m_id == m_pConfigManager->m_debugID){
			m_pLogger->debug("新增数据, m_historyCount = {}, history size = {}", m_historyCount, m_historyTrajectoryInfo.historyTrajectory.size());
		}
	}

}

void ObjectTracking::nonMotorVehivleHeadingSmooth(const common_msgs::sensorobject& object, const double& timeStamp){
	Eigen::VectorXd statementUpdated = m_kalmanFilter.getX();

	bool is_update_history = false;

	//x y w l h yaw vx vy
	std::vector<float> updatedStateInfo = {statementUpdated(0), statementUpdated(1), statementUpdated(2), statementUpdated(3), statementUpdated(4), statementUpdated(5),statementUpdated(6), statementUpdated(7)};
	ObjectPositionInfo objectPositionInfo = packageObjectPositionInfo(updatedStateInfo,
																		timeStamp,
																		object);
	
	// 基于距离保留轨迹，最多40帧，最大10米
	ObjectPositionInfo front_traj = m_historyTrajectoryInfo.historyTrajectory[0];
	double delta_x_front = front_traj.position[0] - objectPositionInfo.position[0];
	double delta_y_front = front_traj.position[1] - objectPositionInfo.position[1];
	double distance_front = sqrt(pow(delta_x_front, 2) + pow(delta_y_front, 2));
	float timeGap = abs(front_traj.timeStamp - objectPositionInfo.timeStamp);
	float objectUpdatedSpeed = sqrt(updatedStateInfo[6] * updatedStateInfo[6] + updatedStateInfo[7] * updatedStateInfo[7]);
	
	if(!m_historyTrajectoryInfo.historyTrajectory.empty()){
		m_historyTrajectoryInfo.historyTrajectory.pop_back();
	}

	if (timeGap != 0 && ((m_historyTrajectoryInfo.historyTrajectory.size() > 20) || (distance_front > 10 && m_historyTrajectoryInfo.historyTrajectory.size() > 1))) // 10.0
	{
		m_historyTrajectoryInfo.historyTrajectory.erase(m_historyTrajectoryInfo.historyTrajectory.begin());
	}

	if (m_historyTrajectoryInfo.historyTrajectory.size() < 1)
	{
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
	}
	else
	{
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
		is_update_history = true;
	}


	//历史轨迹修正航向角：计算kalman更新当前帧航向角后使用历史轨迹修正航向角
	int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
	if (this->m_id == m_pConfigManager->m_debugID)
		cout << "\nkalmantracker update id = " << this->m_id
				<< ", historySize = " << m_historyTrajectoryInfo.historyTrajectory.size()
				<< ", timeGap = " << timeGap 
				<< std::endl;
	// 历史数据有值
	if (historySize >= 4){
		// 最后一帧的历史航向角
		if(m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree < 0){
			double angleRad = m_cCommon.normalizeAngle(m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree * M_PI / 180.0);
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = angleRad * 180.0 / M_PI;
		}
		
		
		// 历史帧数大于阈值才计算轨迹航向角
		ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
		ObjectPositionInfo penultimate = m_historyTrajectoryInfo.historyTrajectory[historySize - 2];
		float objectAngleDegreeInNorth_Clockwise = m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree;
		ObjectPositionInfo objectInfoToCaculate;
		float caculateSpeed = sqrt(bottom.absoluteSpeedX * bottom.absoluteSpeedX + bottom.absoluteSpeedY * bottom.absoluteSpeedY);;
		bool isCaculateHeadingSuccess = false; //是否使用计算航向角
		int objectInfoToCaculateIndex = historySize - 1;
		float caculateDistance = 0;

		double curDetectedHeadingDegree = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading);
		double curReverseHeadingDegree = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading) + 180.0;
		if(curReverseHeadingDegree >= 360.0)
		curReverseHeadingDegree -= 360.0;

		
		float bottomSpeed = sqrt(bottom.absoluteSpeedX * bottom.absoluteSpeedX + bottom.absoluteSpeedY * bottom.absoluteSpeedY);
		if(abs(bottomSpeed) < 2){
			// if(cos(bottom.yawDegree * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.9){// 针对掉头、转弯情况
			// 	// 当前帧与上一帧角度变化小，使用当前帧角度
			// 	if (this->m_id == m_pConfigManager->m_debugID)
			// 	{
			// 		float objectAngleDegreeInCarBackRFU_Clockwise = 
			// 				m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
			// 		cout << "[kalmantracker nonMontorvehiclesmooth] 低速1 使用跟踪角度"
			// 			<< "\n[当前跟踪角度] = " << m_trackingBoxResult[5] * 180 / M_PI
			// 			<< ", bottom.yawDegree = " << bottom.yawDegree
			// 			<< ", 轨迹角度 = " << objectAngleDegreeInNorth_Clockwise
			// 			<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
			// 			<< std::endl;
			// 	}
			// 	return;
			// }
			// else if(cos(curDetectedHeadingDegree * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.9){// 针对掉头、转弯情况下，检测航向角反转180度
			// 	// 当前帧跟踪与检测反转180度变化小，跟踪角度错误，使用上一帧角度
			// 	if (this->m_id == m_pConfigManager->m_debugID)
			// 	{
			// 		float objectAngleDegreeInCarBackRFU_Clockwise = 
			// 				m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
			// 		cout << "[kalmantracker update] 低速2 使用跟踪角度-检测反转180"
			// 			<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
			// 			<< ", [curDetectedHeadingDegree] = " << curDetectedHeadingDegree
			// 			<< ", 轨迹角度 = " << objectAngleDegreeInNorth_Clockwise
			// 			<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
			// 			<< std::endl;
			// 	}
			// 	if(is_update_history){
			// 		m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
			// 			= curDetectedHeadingDegree;
			// 	}
			// 	// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			// 	m_trackingBoxResult[5] = curDetectedHeadingDegree * M_PI / 180.0;
			// 	return;
			// }

			// 速度小，使用第一帧航向角
			double firstHeadingDegreeInNorth = m_historyTrajectoryInfo.historyTrajectory[0].yawDegree;
			if (this->m_id == m_pConfigManager->m_debugID)
			{
				float objectAngleDegreeInCarBackRFU_Clockwise = 
						m_sensorAxisTransformation.NorthClockwise2CarBackRFU(firstHeadingDegreeInNorth, m_selfCarGPS.heading);
				cout << "[kalmantracker nonMontorvehiclesmooth] 1 使用第一帧轨迹角度"
					<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
					<< ", [第一帧跟踪角度] = " << firstHeadingDegreeInNorth
					<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
					<< ", objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
					<< ", gps heading = " << m_selfCarGPS.heading
					<< std::endl;
			}
			if(is_update_history){
				m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
					= firstHeadingDegreeInNorth;
			}
			// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			m_trackingBoxResult[5] = firstHeadingDegreeInNorth * M_PI / 180.0;
			m_kalmanFilter.setHeading(firstHeadingDegreeInNorth * M_PI / 180.0);
			m_measurement(5) = firstHeadingDegreeInNorth * M_PI / 180.0;
		}
		else{
			// 速度大，计算速度，计算速度与跟踪速度都大的情况下
			for(int i = historySize - 2; i >= 0; i--){
				ObjectPositionInfo penultimate = m_historyTrajectoryInfo.historyTrajectory[i];
				double deltaX = bottom.position[0] - penultimate.position[0];
				double deltaY = bottom.position[1] - penultimate.position[1];
				double distance = sqrt(pow(deltaX, 2) + pow(deltaY, 2));
				double deltaT = bottom.timeStamp - penultimate.timeStamp;
				
				if(distance < 1)
					continue;
				
				objectAngleDegreeInNorth_Clockwise = atan2(deltaX, deltaY + 1e-6) * 180 / M_PI;
				if (objectAngleDegreeInNorth_Clockwise < 0){
					objectAngleDegreeInNorth_Clockwise += 360.0;
				}
				if(objectAngleDegreeInNorth_Clockwise >= 360){
					objectAngleDegreeInNorth_Clockwise -= 360.0;
				}

				if (this->m_id == m_pConfigManager->m_debugID)
				{
					float objectAngleDegreeInCarBackRFU_Clockwise = 
						m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
		
					cout
						<< "[kalmantracker update] 计算历史航向角，distance = " 
						<< distance 
						<< ", deltaX = " << deltaX
						<< ", deltaY = " << deltaY
						<< ", deltaT = " << deltaT
						<< ", 轨迹航向角（north）= " << objectAngleDegreeInNorth_Clockwise
						<< ", gps heading = " << m_selfCarGPS.heading
						<< ", bottom.yawDegree = " << bottom.yawDegree
						<< ", penultimate.yawDegree = " << penultimate.yawDegree
						<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
						<< ", 第一帧航向角 = " << m_historyTrajectoryInfo.historyTrajectory[0].yawDegree
						<< std::endl;
				}
				objectInfoToCaculate = m_historyTrajectoryInfo.historyTrajectory[i];
				objectInfoToCaculateIndex = i;
				caculateSpeed = distance / (deltaT + 1e-6);
				caculateDistance = distance;
				isCaculateHeadingSuccess = true;
				break;
			}

			if(isCaculateHeadingSuccess ){ // && bottomSpeed * caculateSpeed > 0 && abs(bottomSpeed - caculateSpeed) < 1
				// 位移大
				if(cos(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0 - m_trackingBoxResult[5]) > 0.9){
					// 跟踪与计算角度差值小，使用跟踪结果
					// 更改第一帧的航向角，使之后计算的航向角以此为基准
					for(size_t i = 0; i < m_historyTrajectoryInfo.historyTrajectory.size(); ++i){
						m_historyTrajectoryInfo.historyTrajectory[i].yawDegree = m_trackingBoxResult[5] * 180 / M_PI;
					}
					if (this->m_id == m_pConfigManager->m_debugID)
					{
						float objectAngleDegreeInCarBackRFU_Clockwise = 
								m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
						cout << "[kalmantracker nonMontorvehiclesmooth]  2 使用跟踪航向角"
							<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
							<< ", bottom degree = " << bottom.yawDegree
							<< ", 计算轨迹objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
							<< ", 检测角度objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
							<< ", gps heading = " << m_selfCarGPS.heading
							<< std::endl;
					}
				}
				else if(cos(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0 - bottom.yawDegree * M_PI / 180.0) > 0.9){
					if (this->m_id == m_pConfigManager->m_debugID)
					{
						float objectAngleDegreeInCarBackRFU_Clockwise = 
								m_sensorAxisTransformation.NorthClockwise2CarBackRFU(objectAngleDegreeInNorth_Clockwise, m_selfCarGPS.heading);
						cout << "[kalmantracker nonMontorvehiclesmooth] 3 使用计算航向角"
							<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
							<< ", bottom degree = " << bottom.yawDegree
							<< ", 计算轨迹objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
							<< ", 检测角度objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
							<< ", gps heading = " << m_selfCarGPS.heading
							<< std::endl;
					}

					if(is_update_history){
						m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
							= objectAngleDegreeInNorth_Clockwise;
					}
					// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
					m_trackingBoxResult[5] = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
					m_kalmanFilter.setHeading(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0);
					m_measurement(5) = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
					// 更改第一帧的航向角，使之后计算的航向角以此为基准
					for(size_t i = 0; i < m_historyTrajectoryInfo.historyTrajectory.size(); ++i){
						m_historyTrajectoryInfo.historyTrajectory[i].yawDegree = objectAngleDegreeInNorth_Clockwise;
					}
					
				}
				else if(cos(m_trackingBoxResult[5] - penultimate.yawDegree * M_PI / 180.0) < 0.9){
					// 认为前一跟踪航向角准确，判断当前航向角是否准确，不准确的话，使用前一帧跟踪结果
					if (this->m_id == m_pConfigManager->m_debugID)
					{
						float objectAngleDegreeInCarBackRFU_Clockwise = 
								m_sensorAxisTransformation.NorthClockwise2CarBackRFU(penultimate.yawDegree, m_selfCarGPS.heading);
						cout << "[kalmantracker nonMontorvehiclesmooth] 4 使用上一帧跟踪航向角"
							<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
							<< ", bottom degree = " << bottom.yawDegree
							<< ", [penultimate degree] = " << penultimate.yawDegree
							<< ", 计算轨迹objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
							<< ", 检测角度objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
							<< ", gps heading = " << m_selfCarGPS.heading
							<< std::endl;
					}

					// 如果跟踪角度与检测相差小，优先使用检测航向角，
					if(is_update_history){
						m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
							= penultimate.yawDegree;
					}
					// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
					m_trackingBoxResult[5] = penultimate.yawDegree * M_PI / 180.0;
					m_kalmanFilter.setHeading(penultimate.yawDegree * M_PI / 180.0);
					m_measurement(5) = penultimate.yawDegree * M_PI / 180.0;
					// 更改第一帧的航向角，使之后计算的航向角以此为基准
					for(size_t i = 0; i < m_historyTrajectoryInfo.historyTrajectory.size(); ++i){
						m_historyTrajectoryInfo.historyTrajectory[i].yawDegree = penultimate.yawDegree;
					}
					
					
				}
				else if(cos(m_trackingBoxResult[5] - bottom.yawDegree * M_PI / 180.0) > 0.9){
					// 跟踪准确的话，使用跟踪角度
					if (this->m_id == m_pConfigManager->m_debugID)
					{
						float objectAngleDegreeInCarBackRFU_Clockwise = 
								m_sensorAxisTransformation.NorthClockwise2CarBackRFU(bottom.yawDegree, m_selfCarGPS.heading);
						cout << "[kalmantracker nonMontorvehiclesmooth] 5 使用跟踪航向角"
							<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
							<< ", bottom degree = " << bottom.yawDegree
							<< ", 计算轨迹objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
							<< ", 检测角度objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
							<< ", gps heading = " << m_selfCarGPS.heading
							<< std::endl;
					}
				}
				else {
					if (this->m_id == m_pConfigManager->m_debugID)
					{
						float objectAngleDegreeInCarBackRFU_Clockwise = 
								m_sensorAxisTransformation.NorthClockwise2CarBackRFU(bottom.yawDegree, m_selfCarGPS.heading);
						cout << "[kalmantracker nonMontorvehiclesmooth] 6 使用跟踪航向角"
							<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
							<< ", bottom degree = " << bottom.yawDegree
							<< ", 计算轨迹objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
							<< ", 检测角度objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
							<< ", gps heading = " << m_selfCarGPS.heading
							<< std::endl;
					}
				}
			}
			else{
				// 位移都很小，说明没有运动，直接取第一帧航向角
				double lastFourdegree = m_historyTrajectoryInfo.historyTrajectory[historySize - 4].yawDegree;
				if (this->m_id == m_pConfigManager->m_debugID)
				{
					float objectAngleDegreeInCarBackRFU_Clockwise = 
							m_sensorAxisTransformation.NorthClockwise2CarBackRFU(lastFourdegree, m_selfCarGPS.heading);
					cout << "[kalmantracker nonMontorvehiclesmooth] 7 使用倒数第四帧跟踪角度"
						<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
						<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
						<< ", objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
						<< ", gps heading = " << m_selfCarGPS.heading
						<< ", [倒数第四帧跟踪角度] = " << lastFourdegree
						<< ", isCaculateHeadingSuccess = " << isCaculateHeadingSuccess 
						<< ", bottomSpeed = " << bottomSpeed 
						<< ", caculateSpeed = " << caculateSpeed 
						<< ", 符号判断= " << (bottomSpeed * caculateSpeed > 0)
						<< ", 差值判断 = " << (abs(bottomSpeed - caculateSpeed) < 1)

						<< std::endl;
				}
				// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
				m_trackingBoxResult[5] = lastFourdegree * M_PI / 180.0;
			}
		}
			
	}
	else{//historySize < 4
		// 自行车固定：跟踪是行人或者聚类，检测是自行车，设定是自行车
		if((m_classification == COMMON::LidarDetectionClassification::Pedestrian
			|| m_classification == COMMON::LidarDetectionClassification::Unknown)
			&& object.classification == COMMON::LidarDetectionClassification::Bicycle){
				cout<<FGRN("classification修正：更新为自行车：") <<", 更新 id = "<< m_id 
					<<", 跟踪类别 = "<<m_classification
					<<", 检测类别 = "<<static_cast<int>(object.classification)
					<<", 跟踪航向角 = "<<m_trackingBoxResult[5] * 180 / M_PI
					<<", 更新为检测航向角 = "<<object.azimuth * 180 / M_PI
					<< endl;
				// m_classification = object.classification;
				// m_trackingBoxResult[2] = object.length;
				// m_trackingBoxResult[3] = object.width;
				// m_trackingBoxResult[4] = object.height;

				float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(object.azimuth * 180 / M_PI, m_selfCarGPS.heading);
				// if(is_update_history){
				// 	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
				// 		= objectAngleDegreeInNorth_Clockwise;
				// }
				// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
				m_trackingBoxResult[5] = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
				// m_kalmanFilter.setHeading(objectAngleDegreeInNorth_Clockwise * M_PI / 180.0);
				// m_measurement(5) = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;

				// m_classificationCount[COMMON::LidarDetectionClassification::Bicycle].detectedTimes++;
		}
		else if(m_classification == COMMON::LidarDetectionClassification::Bicycle
			&& object.classification == COMMON::LidarDetectionClassification::Pedestrian){
				m_pLogger->info("classification修正：自行车不更新: 更新 id = {}, 跟踪类别 = {}, 检测类别= {}", 
							m_id,m_classification,static_cast<int>(object.classification));

				int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
				if(historySize >= 2){
					// if(is_update_history){
					// 	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = m_historyTrajectoryInfo.historyTrajectory[historySize-2].yawDegree;
					// }
					// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
					m_trackingBoxResult[5] = m_historyTrajectoryInfo.historyTrajectory[historySize-2].yawDegree * M_PI / 180.0;
					// m_kalmanFilter.setHeading(m_historyTrajectoryInfo.historyTrajectory[historySize-2].yawDegree * M_PI / 180.0);
					// m_measurement(5) = m_historyTrajectoryInfo.historyTrajectory[historySize-2].yawDegree * M_PI / 180.0;
				}
				else{
					double firstHeadingDegreeInNorth = m_historyTrajectoryInfo.historyTrajectory[0].yawDegree;
					// if(is_update_history){
					// 	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
					// 		= firstHeadingDegreeInNorth;
					// }
					// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
					m_trackingBoxResult[5] = firstHeadingDegreeInNorth * M_PI / 180.0;
					// m_kalmanFilter.setHeading(firstHeadingDegreeInNorth * M_PI / 180.0);
					// m_measurement(5) = firstHeadingDegreeInNorth * M_PI / 180.0;
				}
				// 跟踪是自行车，检测是行人，设定是自行车
				// m_classification = COMMON::LidarDetectionClassification::Bicycle;
				// m_classificationCount[COMMON::LidarDetectionClassification::Bicycle].detectedTimes++;
				// object.length = m_trackingBoxResult[2];
				// object.width = m_trackingBoxResult[3];
				// object.height = m_trackingBoxResult[4];
		}
		else{
			double firstHeadingDegreeInNorth = m_historyTrajectoryInfo.historyTrajectory[0].yawDegree;
			if (this->m_id == m_pConfigManager->m_debugID)
			{
				float objectAngleDegreeInCarBackRFU_Clockwise = 
						m_sensorAxisTransformation.NorthClockwise2CarBackRFU(firstHeadingDegreeInNorth, m_selfCarGPS.heading);
				cout << "[kalmantracker update] 0 使用第一帧检测角度"
					<< "\n当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
					<< ", [第一帧跟踪角度] = " << firstHeadingDegreeInNorth
					<< ", objectAngleDegreeInCarBackRFU_Clockwise = " << objectAngleDegreeInCarBackRFU_Clockwise
					<< ", objectAngleInCarBackRFU_Clockwise = " << object.azimuth * 180 / M_PI
					<< ", gps heading = " << m_selfCarGPS.heading
					<< std::endl;
			}
			
			// if(is_update_history){
			// 	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
			// 		= firstHeadingDegreeInNorth;
			// }
			// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
			m_trackingBoxResult[5] = firstHeadingDegreeInNorth * M_PI / 180.0;
			// m_kalmanFilter.setHeading(firstHeadingDegreeInNorth * M_PI / 180.0);
			// m_measurement(5) = firstHeadingDegreeInNorth * M_PI / 180.0;
		}
	}

}

// TODO 针对聚类目标进行处理：速度小0，锁定航向角
void ObjectTracking::clusterObjectsHeadingSmooth(const common_msgs::sensorobject& object, const double& timeStamp){
	Eigen::VectorXd statementUpdated = m_kalmanFilter.getX();

	bool is_update_history = false;

	//x y w l h yaw vx vy
	std::vector<float> updatedStateInfo = {statementUpdated(0), statementUpdated(1), statementUpdated(2), statementUpdated(3), statementUpdated(4), statementUpdated(5),statementUpdated(6), statementUpdated(7)};
	ObjectPositionInfo objectPositionInfo = packageObjectPositionInfo(updatedStateInfo,
																		timeStamp,
																		object);
	
	// 基于距离保留轨迹，最多40帧，最大10米
	if( m_historyTrajectoryInfo.historyTrajectory.empty())
		return;
	ObjectPositionInfo front_traj = m_historyTrajectoryInfo.historyTrajectory[0];
	double delta_x_front = front_traj.position[0] - objectPositionInfo.position[0];
	double delta_y_front = front_traj.position[1] - objectPositionInfo.position[1];
	double distance_front = sqrt(pow(delta_x_front, 2) + pow(delta_y_front, 2));
	float timeGap = abs(front_traj.timeStamp - objectPositionInfo.timeStamp);
	float objectUpdatedSpeed = sqrt(updatedStateInfo[6] * updatedStateInfo[6] + updatedStateInfo[7] * updatedStateInfo[7]);
	
	if(!m_historyTrajectoryInfo.historyTrajectory.empty()){
		m_historyTrajectoryInfo.historyTrajectory.pop_back();
	}

	if (timeGap != 0 && ((m_historyTrajectoryInfo.historyTrajectory.size() > 20) || (distance_front > 10 && m_historyTrajectoryInfo.historyTrajectory.size() > 1))) // 10.0
	{
		m_historyTrajectoryInfo.historyTrajectory.erase(m_historyTrajectoryInfo.historyTrajectory.begin());
	}

	if (m_historyTrajectoryInfo.historyTrajectory.size() < 1)
	{
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
	}
	else
	{
		m_historyTrajectoryInfo.historyTrajectory.emplace_back(objectPositionInfo);
		is_update_history = true;
	}

	common_msgs::sensorobject sensorobjectTemp = object;
	auto xMinMax = std::minmax_element(sensorobjectTemp.points.begin(), sensorobjectTemp.points.end(),[](const common_msgs::point3d& point1,const common_msgs::point3d& point2){
		return point1.x < point2.x;
	});
	float xMinMaxDistance = (xMinMax.second)->x - (xMinMax.first)->x;
	auto yMinMax = std::minmax_element(sensorobjectTemp.points.begin(), sensorobjectTemp.points.end(),[](const common_msgs::point3d& point1,const common_msgs::point3d& point2){
		return point1.y < point2.y;
	});
	float yMinMaxDistance = (yMinMax.second)->y - (yMinMax.first)->y;
	float widthTemp = sensorobjectTemp.width, lengthTemp = sensorobjectTemp.length;
	sensorobjectTemp.width = min(widthTemp, lengthTemp);
	sensorobjectTemp.length = max(widthTemp, lengthTemp);
	sensorobjectTemp.width = min(sensorobjectTemp.width, min(xMinMaxDistance, yMinMaxDistance));
	sensorobjectTemp.length = min(sensorobjectTemp.length, max(xMinMaxDistance, yMinMaxDistance));
	// assert(sensorobjectTemp.width <= sensorobjectTemp.length);

	// cout << "[kalmantracker clustersmooth] id = " << this->m_id
	// 				<< ", widthTemp = " <<widthTemp
	// 				<< ", lengthTemp  = " << lengthTemp
	// 				<<", xMinMaxDistance = " << xMinMaxDistance
	// 				<< ", yMinMaxDistance = " << yMinMaxDistance
	// 				<< ", sensorobjectTemp.width = " << sensorobjectTemp.width
	// 				<< ", sensorobjectTemp.length = " << sensorobjectTemp.length
	// 				<< std::endl;


	findOptimalYaw(sensorobjectTemp);
	float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(sensorobjectTemp.azimuth * 180 / M_PI, m_selfCarGPS.heading);
	// cout << "[kalmantracker clustersmooth] id = " << this->m_id
	// 				<< ", object heading degree = " <<object.azimuth * 180.0 / M_PI
	// 				<< ", sensorobjectTemp.azimuth  = " << sensorobjectTemp.azimuth * 180 / M_PI
	// 				<<", m_selfCarGPS.heading = " << m_selfCarGPS.heading
	// 				<< ", 当前跟踪角度 = " << m_trackingBoxResult[5] * 180 / M_PI
	// 				<< ", objectAngleDegreeInNorth_Clockwise = " << objectAngleDegreeInNorth_Clockwise
	// 				<< std::endl;
	int historySize = m_historyTrajectoryInfo.historyTrajectory.size();
	if(is_update_history){
		m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree
			= objectAngleDegreeInNorth_Clockwise;
	}
	// 跟踪目标 tracker里的航向角，状态向量里的航向角，变为计算出的后轴航向角
	m_headingRadInNorthClockwise = objectAngleDegreeInNorth_Clockwise * M_PI / 180.0;
	m_trackingBoxResult[5] = m_headingRadInNorthClockwise;
	m_kalmanFilter.setHeading(m_headingRadInNorthClockwise);
}


/***
 * 计算向量叉积（用于判断点是否在矩形内）
 * @param cornerPoint1 （矩形）第一个点
 * @param cornerPoint2 （矩形）第二个点
 * @param point 点（用到检测的点坐标）
 * @return 叉积值
 */
float ObjectTracking::getCross(const common_msgs::point3d& cornerPoint1, const common_msgs::point3d& cornerPoint2,
                            const common_msgs::point3d& point){
	return (cornerPoint2.x - cornerPoint1.x) * (point.y - cornerPoint1.y) - (point.x - cornerPoint1.x) * (cornerPoint2.y - cornerPoint1.y);
}

// 计算聚类最优航向角
void ObjectTracking::findOptimalYaw(common_msgs::sensorobject& sensorObject){
	float rawHeadingDegree = sensorObject.azimuth * 180.0 / M_PI;
	float bestHeadingDegree = 0;
	int maxPointsNumberInBox = 0;
	vector<double> boxInfo(7, 0.0);
	vector<vector<double>> pointsVector(8, vector<double>(3, 0.0));
	common_msgs::point3d cornerPoint0, cornerPoint1, cornerPoint2, cornerPoint3;

	// cout <<"findOptimalYaw: id = " << this->m_id <<"， sensorID = " << sensorObject.id << ", rawHeadingDegree = " << rawHeadingDegree 
	// 	<< ", all points = " << sensorObject.points.size() << endl;
	
	float hedingDegreeLowerlimit = sensorObject.azimuth * 180.0 / M_PI - 20;
	if(hedingDegreeLowerlimit < 0){
		hedingDegreeLowerlimit += 360;
	}
	float headingDegreeUpperlimit = sensorObject.azimuth * 180.0 / M_PI + 20;
	if(headingDegreeUpperlimit >= 360){
		headingDegreeUpperlimit -= 360;
	}

	for(float headingDegree = hedingDegreeLowerlimit; headingDegree < headingDegreeUpperlimit; headingDegree += 5){
		if(headingDegree >= 360){
			headingDegree -= 360;
		}
		float headingClockwise = 2.0 * M_PI - headingDegree * M_PI / 180.0;
		boxInfo = {sensorObject.x, sensorObject.y, sensorObject.z,
					sensorObject.length, sensorObject.width, sensorObject.height,
			           headingClockwise};
		pointsVector = m_cCommon.boxes_to_corners_3d(boxInfo);

		cornerPoint0.x = pointsVector[0][0]; cornerPoint0.y = pointsVector[0][1]; cornerPoint0.z = pointsVector[0][2];
		cornerPoint1.x = pointsVector[1][0]; cornerPoint1.y = pointsVector[1][1]; cornerPoint1.z = pointsVector[1][2];
		cornerPoint2.x = pointsVector[2][0]; cornerPoint2.y = pointsVector[2][1]; cornerPoint2.z = pointsVector[2][2];
		cornerPoint3.x = pointsVector[3][0]; cornerPoint3.y = pointsVector[3][1]; cornerPoint3.z = pointsVector[3][2];
	
		int pointsNumberInBox = 0;
		for(const common_msgs::point3d& point : sensorObject.points){
			bool isPointInBox = ((getCross(cornerPoint0, cornerPoint1,point) * getCross(cornerPoint2, cornerPoint3,point)) >= 0) &&
		                    ((getCross(cornerPoint1, cornerPoint2,point) * getCross(cornerPoint3, cornerPoint0,point)) >= 0);
			if(isPointInBox){
				++pointsNumberInBox;
			}
		}

		if(pointsNumberInBox > maxPointsNumberInBox){
			maxPointsNumberInBox = pointsNumberInBox;
			bestHeadingDegree = headingDegree;
		}
		// cout <<"findOptimalYaw: id = " << this->m_id <<"， sensorID = "  << sensorObject.id << ", headingDegree = " << headingDegree 
		// 	<< ", pointsNumberInBox = " << pointsNumberInBox << endl;
	}

	if(cos((rawHeadingDegree - bestHeadingDegree) * M_PI / 180.0) > 0.9){
		// 计算的这个角度与检测的航向角相差不大，则使用检测的航向角
		// sensorObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << this->m_id <<"， sensorID = "  << sensorObject.id << ", 计算的这个角度与检测的航向角相差不大,使用检测航向角"  << endl;
	}
	else if(cos((rawHeadingDegree - bestHeadingDegree - 180.0) * M_PI / 180.0) > 0.9){
		// 计算角度+180度与检测的航向角相差不大，则使用检测的航向角
		// sensorObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << this->m_id <<"， sensorID = " << sensorObject.id << ", 计算角度+180度与检测的航向角相差不大,使用检测航向角"  << endl;
	}
	else{
		// 计算角度与检测的航向角相差大，则使用计算的航向角
		sensorObject.azimuth = bestHeadingDegree * M_PI / 180.0;
		// cout <<"findOptimalYaw: id = " << this->m_id <<"， sensorID = "  << sensorObject.id << ", 使用计算航向角"  << endl;
	}

	sensorObject.azimuth = bestHeadingDegree * M_PI / 180.0;
	// cout <<"findOptimalYaw: = " << this->m_id <<"， sensorID = " << sensorObject.id << ", rawHeadingDegree = " << rawHeadingDegree 
	// 	<< ", bestHeadingDegree = " << bestHeadingDegree 
	// 	<<", maxPointsNumberInBox = " << maxPointsNumberInBox << endl;

}

void ObjectTracking::velocitySmooth(Eigen::VectorXd& statement){
	// 预测速度约束
	if(m_hits > 4 && m_hit_streak != 0){
		// 横向速度约束
		if(statement(6) * m_trackingBoxResult[6] > 0){
			if(abs(statement(6) - m_trackingBoxResult[6]) > 0.6){
				// 大于加速度6m/s^2,使用上一帧跟踪速度
				if (this->m_id == m_pConfigManager->m_debugID)
					cout << "kalmantracker 预测速度约束1 id = " << this->m_id 
						<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
						<<", 预测横向速度 = " << statement(6)
						// <<", 预测纵向速度 = " << statement(7) 
						<<", 自车横向速度 = " << m_selfCarSpeed[0]
						// <<", 自车纵向速度 = " << m_selfCarSpeed[1] 
						<<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
						// <<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
						<< std::endl;

				statement(6) = m_trackingBoxResult[6];
				m_kalmanFilter.setVx(statement(6));
			}
		}
		else if(statement(6) * m_trackingBoxResult[6] < 0){
			// 预测反向速度，不使用预测速度，使用上一帧跟踪速度
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "kalmantracker 预测速度约束2 id = " << this->m_id 
					<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
					<<", 预测横向速度 = " << statement(6)
					// <<", 预测纵向速度 = " << statement(7) 
					<<", 自车横向速度 = " << m_selfCarSpeed[0]
					// <<", 自车纵向速度 = " << m_selfCarSpeed[1] 
					<<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
					// <<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
					<< std::endl;
			statement(6) = m_trackingBoxResult[6];
			m_kalmanFilter.setVx(statement(6));
		}
		else{
			// 有零值，暂时使用预测速度
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "kalmantracker 预测速度约束3 id = " << this->m_id 
					<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
					<<", 预测横向速度 = " << statement(6)
					// <<", 预测纵向速度 = " << statement(7) 
					<<", 自车横向速度 = " << m_selfCarSpeed[0]
					// <<", 自车纵向速度 = " << m_selfCarSpeed[1] 
					<<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
					// <<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
					<< std::endl;
			
		}

		// 纵向速度约束
		if(statement(7) * m_trackingBoxResult[7] > 0){
			if(abs(statement(7) - m_trackingBoxResult[7]) > 0.6){
				// 大于加速度6m/s^2,使用上一帧跟踪速度
				if (this->m_id == m_pConfigManager->m_debugID)
					cout << "kalmantracker 预测速度约束1 id = " << this->m_id 
						<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
						// <<", 预测横向速度 = " << statement(6)
						<<", 预测纵向速度 = " << statement(7) 
						// <<", 自车横向速度 = " << m_selfCarSpeed[0]
						<<", 自车纵向速度 = " << m_selfCarSpeed[1] 
						// <<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
						<<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
						<< std::endl;

				statement(7) = m_trackingBoxResult[7];
				m_kalmanFilter.setVy(statement(7));
			}
			else{
				if (this->m_id == m_pConfigManager->m_debugID)
					cout << "kalmantracker 预测速度约束1-else id = " << this->m_id 
						<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
						// <<", 预测横向速度 = " << statement(6)
						<<", 预测纵向速度 = " << statement(7) 
						// <<", 自车横向速度 = " << m_selfCarSpeed[0]
						<<", 自车纵向速度 = " << m_selfCarSpeed[1] 
						// <<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
						<<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
						<< std::endl;
			}
		}
		else if(statement(7) * m_trackingBoxResult[7] < 0){
			// 预测反向速度，不使用预测速度，使用上一帧跟踪速度
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "kalmantracker 预测速度约束2 id = " << this->m_id 
					<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
					// <<", 预测横向速度 = " << statement(6)
					<<", 预测纵向速度 = " << statement(7) 
					// <<", 自车横向速度 = " << m_selfCarSpeed[0]
					<<", 自车纵向速度 = " << m_selfCarSpeed[1] 
					// <<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
					<<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
					<< std::endl;
			statement(7) = m_trackingBoxResult[7];
			m_kalmanFilter.setVy(statement(7));
		}
		else{
			// 有零值，暂时使用预测速度
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "kalmantracker 预测速度约束3 id = " << this->m_id 
					<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
					// <<", 预测横向速度 = " << statement(6)
					<<", 预测纵向速度 = " << statement(7) 
					// <<", 自车横向速度 = " << m_selfCarSpeed[0]
					<<", 自车纵向速度 = " << m_selfCarSpeed[1] 
					// <<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
					<<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
					<< std::endl;
			
		}
	}
	else{
		if (this->m_id == m_pConfigManager->m_debugID)
			cout << "kalmantracker 预测速度约束0 id = " << this->m_id 
				<< ", historyTrajectory size :" << m_historyTrajectoryInfo.historyTrajectory.size()
				<<", 预测横向速度 = " << statement(6)
				<<", 预测纵向速度 = " << statement(7) 
				<<", 自车横向速度 = " << m_selfCarSpeed[0]
				<<", 自车纵向速度 = " << m_selfCarSpeed[1] 
				<<", 上一帧跟踪横向速度 = " << m_trackingBoxResult[6]
				<<", 上一帧跟踪纵向速度 = " << m_trackingBoxResult[7] 
				<<", m_hits = " << m_hits 
				<<", m_hit_streak = " << m_hit_streak
				<< std::endl;
	}
}


void ObjectTracking::resetTrackingObjectInfo(ObjectPositionInfo& bottom, ObjectPositionInfo& penultimate, const int& trackingState){
	int historySize = m_historyTrajectoryInfo.historyTrajectory.size(); 
	if(historySize < 2)
		return;
	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[0] = penultimate.position[0];
	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[1] = penultimate.position[1];

	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].length = penultimate.length;
	m_historyTrajectoryInfo.historyTrajectory[historySize - 1].width = penultimate.width;

	m_trackingBoxResult[0] = penultimate.position[0];
	m_trackingBoxResult[1] = penultimate.position[1];
	m_kalmanFilter.setX(penultimate.position[0]);
	m_kalmanFilter.setY(penultimate.position[1]);
	// 速度重置
	m_trackingBoxResult[6] = 0;
	m_trackingBoxResult[7] = 0;
	m_kalmanFilter.setVx(0);
	m_kalmanFilter.setVy(0);
	// 航向角使用上一帧跟踪数据
	// m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = penultimate.yawDegree;
	// m_trackingBoxResult[5] = penultimate.yawDegree * M_PI / 180.0;
	// m_kalmanFilter.setHeading(penultimate.yawDegree * M_PI / 180.0);
	
	// if(trackingState == TrackingState::Update){
	// 	m_measurement(0) = penultimate.position[0];
	// 	m_measurement(1) = penultimate.position[1];
	// 	m_measurement(5) = penultimate.yawDegree * M_PI / 180.0;
	// 	if(m_stateNum == 8){
	// 		m_measurement(6) = 0;
	// 		m_measurement(7) = 0;
	// 	}
	// }
	if (this->m_id == m_pConfigManager->m_debugID)
		cout << "[kalmantracker positionSmooth 速度置0-3]"
			<< std::endl;
}

void ObjectTracking::positionSmooth(const int& trackingState){
	float distanceThreshold = 0.5;
	// 需要考虑目标加速超过1，减速到0以及等红绿灯时的低速缓行情况，锁定会位置会有迟滞
	// 针对车辆、行人、非机动车进行分别处理
	if(this->m_classification == COMMON::LidarDetectionClassification::Bus || this->m_classification == COMMON::LidarDetectionClassification::Truck){

		int historySize = m_historyTrajectoryInfo.historyTrajectory.size(); 
		if(historySize < 5)
			return;
		ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
		ObjectPositionInfo penultimate = m_historyTrajectoryInfo.historyTrajectory[historySize - 5];

		double deltaX = bottom.position[0] - penultimate.position[0];
		double deltaY = bottom.position[1] - penultimate.position[1];
		double deltaT = bottom.timeStamp - penultimate.timeStamp;
		double distance = sqrt(pow(deltaX, 2) + pow(deltaY, 2));
		float caculateSpeed = distance / (deltaT + 1e-6);
		float caculateSpeedVe = deltaX / (deltaT + 1e-6);
		float caculateSpeedVn = deltaY / (deltaT + 1e-6);
		// 当前帧速度与上一帧速度都小于阈值，且位移小于0.5m，
		float bottomSpeed = sqrt(bottom.absoluteSpeedX * bottom.absoluteSpeedX + bottom.absoluteSpeedY * bottom.absoluteSpeedY);
		float penultimateSpeed = sqrt(penultimate.absoluteSpeedX * penultimate.absoluteSpeedX + penultimate.absoluteSpeedY * penultimate.absoluteSpeedY);
		// 大巴车尺寸跳变
		if(bottomSpeed < 1 && caculateSpeed < 1){ // 距离短
			if (this->m_id == m_pConfigManager->m_debugID)
					cout << "[kalmantracker positionSmooth success]bus速度小"
						<< std::endl;
			resetTrackingObjectInfo(bottom, penultimate, trackingState);
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "[kalmantracker positionSmooth success]bus速度小，固定位置"
					<<", bottomSpeed = " << bottomSpeed
					<<", penultimateSpeed = " << penultimateSpeed 
					<< ", historySize = " << historySize
					<< ", distance = " << distance
					<< ", distanceThreshold = " << distanceThreshold
					<< ", caculateSpeed = " << caculateSpeed
					<< ", deltaT = " << deltaT
					<< std::endl;
		}
		// else if(distance < 0.5){
		// 	if (this->m_id == m_pConfigManager->m_debugID)
		// 			cout << "[kalmantracker positionSmooth success]bus距离小"
		// 				<< std::endl;
		// 	resetTrackingObjectInfo(bottom, penultimate, trackingState);
		// 	if (this->m_id == m_pConfigManager->m_debugID)
		// 		cout << "[kalmantracker positionSmooth success]bus距离低，固定位置"
		// 			<<", bottomSpeed = " << bottomSpeed
		// 			<<", penultimateSpeed = " << penultimateSpeed 
		// 			<< ", historySize = " << historySize
		// 			<< ", distance = " << distance
		// 			<< ", distanceThreshold = " << distanceThreshold
		// 			<< ", caculateSpeed = " << caculateSpeed
		// 			<< ", deltaT = " << deltaT
		// 			<< std::endl;
		// }
		else{// 加速
			if (this->m_id == m_pConfigManager->m_debugID)
					cout << "[kalmantracker positionSmooth success]bus，更新位置"
						<<", bottomSpeed = " << bottomSpeed
						<<", penultimateSpeed = " << penultimateSpeed 
						<< ", historySize = " << historySize
						<< ", distance = " << distance
						<< ", distanceThreshold = " << distanceThreshold
						<< ", caculateSpeed = " << caculateSpeed
						<< ", deltaT = " << deltaT
						<< std::endl;
		}
		
	}
	else {//if(this->m_classification == COMMON::LidarDetectionClassification::Car)
		distanceThreshold = 0.5;
		int historySize = m_historyTrajectoryInfo.historyTrajectory.size(); 
		if(historySize < 5)
			return;
		ObjectPositionInfo bottom = m_historyTrajectoryInfo.historyTrajectory[historySize - 1];
		ObjectPositionInfo penultimate = m_historyTrajectoryInfo.historyTrajectory[historySize - 2];
		ObjectPositionInfo thirdInfo = m_historyTrajectoryInfo.historyTrajectory[historySize - 5];

		double deltaX = bottom.position[0] - thirdInfo.position[0];
		double deltaY = bottom.position[1] - thirdInfo.position[1];
		double deltaT = bottom.timeStamp - thirdInfo.timeStamp;
		double distance = sqrt(pow(deltaX, 2) + pow(deltaY, 2));
		float caculateSpeed = distance / (deltaT + 1e-6);
		float caculateSpeedVe = deltaX / (deltaT + 1e-6);
		float caculateSpeedVn = deltaY / (deltaT + 1e-6);
		// 当前帧速度与上一帧速度都小于阈值，且位移小于0.5m，
		float bottomSpeed = sqrt(bottom.absoluteSpeedX * bottom.absoluteSpeedX + bottom.absoluteSpeedY * bottom.absoluteSpeedY);
		float penultimateSpeed = sqrt(penultimate.absoluteSpeedX * penultimate.absoluteSpeedX + penultimate.absoluteSpeedY * penultimate.absoluteSpeedY);

		if (this->m_id == m_pConfigManager->m_debugID)
		cout << "[kalmantracker positionSmooth]car5"
			<< ", historySize = " << historySize
			<<", bottom0 = " << bottom.position[0]
			<<", bottom1 = " << bottom.position[1] 
			<< ",  penultimate.position[0] = " <<  penultimate.position[0]
			<< ",  penultimate.position[1] = " <<  penultimate.position[1]
			<<", bottomSpeed = " << bottomSpeed
			<<", penultimateSpeed = " << penultimateSpeed 
			<< ", distance = " << distance
			<< ", distanceThreshold = " << distanceThreshold
			<< ", caculateSpeed = " << caculateSpeed
			<< ", deltaT = " << deltaT
			<< std::endl;

		if(bottomSpeed < 1 && distance < distanceThreshold){
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[0] = penultimate.position[0];
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[1] = penultimate.position[1];

			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].length = penultimate.length;
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].width = penultimate.width;

			m_trackingBoxResult[0] = penultimate.position[0];
			m_trackingBoxResult[1] = penultimate.position[1];
			m_kalmanFilter.setX(penultimate.position[0]);
			m_kalmanFilter.setY(penultimate.position[1]);

			if(distance < distanceThreshold){
				// 速度重置
				m_trackingBoxResult[6] = 0;
				m_trackingBoxResult[7] = 0;
				m_kalmanFilter.setVx(0);
				m_kalmanFilter.setVy(0);
				// 航向角使用上一帧跟踪数据
				// m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = penultimate.yawDegree;
				// m_trackingBoxResult[5] = penultimate.yawDegree * M_PI / 180.0;
				// m_kalmanFilter.setHeading(penultimate.yawDegree * M_PI / 180.0);

				// if(trackingState == TrackingState::Update){
				// 	m_measurement(5) = penultimate.yawDegree * M_PI / 180.0;
				// 	if(m_stateNum == 8){
				// 		m_measurement(6) = 0;
				// 		m_measurement(7) = 0;
				// 	}
				// }
				if (this->m_id == m_pConfigManager->m_debugID)
					cout << "[kalmantracker positionSmooth 速度置0-1]"
						<< std::endl;
				
			}
			// if(trackingState == TrackingState::Update){
			// 	m_measurement(0) = penultimate.position[0];
			// 	m_measurement(1) = penultimate.position[1];
			// }
			

			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "[kalmantracker positionSmooth success]car1"
					<<", bottom0 = " << bottom.position[0]
					<<", bottom1 = " << bottom.position[1] 
					<<", history0 = " << m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[0]
					<<", history1 = " << m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[1]
					<< ",  penultimate.position[0]" <<  penultimate.position[0]
					<< ",  penultimate.position[1] = " <<  penultimate.position[1]	
					<<", bottomSpeed = " << bottomSpeed
					<<", penultimateSpeed = " << penultimateSpeed 
					<< ", historySize = " << historySize
					<< ", distance = " << distance
					<< ", distanceThreshold = " << distanceThreshold
					<< ", caculateSpeed = " << caculateSpeed
					<< ", deltaT = " << deltaT
					<< std::endl;
		}
		else if (penultimateSpeed < 1 && abs(bottomSpeed - penultimateSpeed) / deltaT >	6){
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[0] = penultimate.position[0];
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].position[1] = penultimate.position[1];

			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].length = penultimate.length;
			m_historyTrajectoryInfo.historyTrajectory[historySize - 1].width = penultimate.width;

			m_trackingBoxResult[0] = penultimate.position[0];
			m_trackingBoxResult[1] = penultimate.position[1];
			m_kalmanFilter.setX(penultimate.position[0]);
			m_kalmanFilter.setY(penultimate.position[1]);
			if(distance < distanceThreshold){
				// 速度重置
				m_trackingBoxResult[6] = 0;
				m_trackingBoxResult[7] = 0;
				m_kalmanFilter.setVx(0);
				m_kalmanFilter.setVy(0);
				// 航向角使用上一帧跟踪数据
				// m_historyTrajectoryInfo.historyTrajectory[historySize - 1].yawDegree = penultimate.yawDegree;
				// m_trackingBoxResult[5] = penultimate.yawDegree * M_PI / 180.0;
				// m_kalmanFilter.setHeading(penultimate.yawDegree * M_PI / 180.0);
				if (this->m_id == m_pConfigManager->m_debugID)
					cout << "[kalmantracker positionSmooth 速度置0-2]"
						<< std::endl;

				// if(trackingState == TrackingState::Update){
				// 	m_measurement(5) = penultimate.yawDegree * M_PI / 180.0;
				// 	if(m_stateNum == 8){
				// 		m_measurement(6) = 0;
				// 		m_measurement(7) = 0;
				// 	}
				// }
			}
			// if(trackingState == TrackingState::Update){
			// 	m_measurement(0) = penultimate.position[0];
			// 	m_measurement(1) = penultimate.position[1];
			// }

			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "[kalmantracker positionSmooth success]car2"
					<<", bottomSpeed = " << bottomSpeed
					<<", penultimateSpeed = " << penultimateSpeed 
					<< ", historySize = " << historySize
					<< ", distance = " << distance
					<< ", distanceThreshold = " << distanceThreshold
					<< ", caculateSpeed = " << caculateSpeed
					<< ", deltaT = " << deltaT
					<< std::endl;
		}
		else{
			if (this->m_id == m_pConfigManager->m_debugID)
				cout << "[kalmantracker positionSmooth falied]"
					<< ", historySize = " << historySize
					<<", bottomSpeed = " << bottomSpeed
					<<", penultimateSpeed = " << penultimateSpeed
					<< ", historySize = " << historySize
					<< ", distance = " << distance
					<< ", distanceThreshold = " << distanceThreshold
					<< ", caculateSpeed = " << caculateSpeed
					<< ", deltaT = " << deltaT
					<< std::endl;
		}
	}
}

/***
 * 封装ObjectPositionInfo结构数据
 * @param objectPositionInUTMAxis UTM坐标系下的目标位置
 * @param updatedStateInfo 目标更新后的状态向量
 * @param timeStamp 当前时间戳
 * @param historyTrajectory 目标历史轨迹
 * @param object 目标观测量(目标检测结果)
 * @return ObjectPositionInfo结构数据
 */
ObjectPositionInfo ObjectTracking::packageObjectPositionInfo(const vector<float>& updatedStateInfo,
                                                             const double& timeStamp,
                                                             const common_msgs::sensorobject& object){
	ObjectPositionInfo objectPositionInfo;
	objectPositionInfo.timeStamp = timeStamp;
	objectPositionInfo.position = {updatedStateInfo[0], updatedStateInfo[1], object.z};
	//20220914 保存经纬度
	wgs84_utm wgs84Utm;
	WGS84Corr lla;
	
	
	wgs84Utm.UTMXYToLatLon(updatedStateInfo[0], updatedStateInfo[1], m_pConfigManager->m_cityUTMCode, false, lla);
	objectPositionInfo.position = {updatedStateInfo[0], updatedStateInfo[1], 0};
	objectPositionInfo.longtitude = lla.log/M_PI*180;
	objectPositionInfo.latitude = lla.lat/M_PI*180;
	objectPositionInfo.altitude = object.height + m_selfCarUTMPosition[2];
	//std::cerr << "update log lat =" << lla.log/M_PI*180 << " " << lla.lat/M_PI*180 << std::endl;
	
	//float objectAngleDegreeInNorth_Clockwise = m_sensorAxisTransformation.CarBackRFUHeading2NorthClockwise(updatedStateInfo[5] * 180 / M_PI, m_selfCarGPS.heading);
	objectPositionInfo.rollDegree = 0;//20220920 degree m_selfCarGPS.roll
	objectPositionInfo.pitchDegree = 0;
	objectPositionInfo.yawDegree = updatedStateInfo[5] * 180 / M_PI;//objectAngleDegreeInNorth_Clockwise;
	// if(objectPositionInfo.yawDegree < 0){
		// cout << "[kalmantracker package] yawDegree = " << objectPositionInfo.yawDegree
		//      << std::endl;
	// }
	
	objectPositionInfo.xInLidarAxis = updatedStateInfo[0];// 20230214 存放目标在当前帧lidar坐标系下的位置，用于目标速度补偿
	objectPositionInfo.yInLidarAxis = updatedStateInfo[1];
	objectPositionInfo.zInLidarAxis = 0;
	
	objectPositionInfo.objectHeadingDegree = updatedStateInfo[5] * 180 / M_PI;//object.azimuth * 180.0 / M_PI;
	
	objectPositionInfo.absoluteSpeedX = updatedStateInfo[6];//UTM速度
	objectPositionInfo.absoluteSpeedY = updatedStateInfo[7];//UTM速度

	objectPositionInfo.length = updatedStateInfo[2];
	objectPositionInfo.width = updatedStateInfo[3];
	objectPositionInfo.classification = static_cast<int>(object.classification);

	return std::move(objectPositionInfo);
}


std::vector<float> ObjectTracking::get_state_finaly(){
	Eigen::VectorXd statementFinally= m_kalmanFilter.getX();
	//x y w l h yaw vx vy
	std::vector<float> updatedStateInfo = {statementFinally(0), statementFinally(1),
										   statementFinally(2), statementFinally(3),
										   statementFinally(4), statementFinally(5),
										   statementFinally(6), statementFinally(7)};
	return std::move(updatedStateInfo);
}


std::vector<float> ObjectTracking::getCurPredictedInfo(){
	return m_curPredictedInfo;
}

std::vector<double> ObjectTracking::getPositionInCarbackRFU(){
	Eigen::Vector3d inputUTMPosition{m_trackingBoxResult[0] - m_selfCarUTMPosition[0],
									m_trackingBoxResult[1] - m_selfCarUTMPosition[1],
									m_objectOriginal.z};
	std::vector<double> objectPositionInCarBackFRU = m_sensorAxisTransformation.ENU2BodyAxis(inputUTMPosition);
	return std::move(objectPositionInCarBackFRU);
}

// RFU坐标系下的对地速度
std::vector<double> ObjectTracking::getSpeedInCarbackRFU(){
	Eigen::Vector3d UTMRelativeSpeed{m_trackingBoxResult[6] - m_selfCarGPS.speedE,
									  m_trackingBoxResult[7] - m_selfCarGPS.speedN,
		                                0};
		
	std::vector<double> objectSpeedInCarBackRFU = m_sensorAxisTransformation.ENU2BodyAxis(UTMRelativeSpeed);
	objectSpeedInCarBackRFU[0] += m_selfCarSpeed[0];
	objectSpeedInCarBackRFU[1] += m_selfCarSpeed[1];
	objectSpeedInCarBackRFU[2] += m_selfCarSpeed[2];
	return std::move(objectSpeedInCarBackRFU);
}

// RFU坐标系下的对地速度
std::vector<double> ObjectTracking::getSpeedInCarbackRFU(const Eigen::Vector3d& speedInUTM) const{
	Eigen::Vector3d UTMRelativeSpeed{speedInUTM[0] - m_selfCarGPS.speedE,
									  speedInUTM[1] - m_selfCarGPS.speedN,
		                                0};
		
	std::vector<double> objectSpeedInCarBackRFU = m_sensorAxisTransformation.ENU2BodyAxis(UTMRelativeSpeed);
	objectSpeedInCarBackRFU[0] += m_selfCarSpeed[0];
	objectSpeedInCarBackRFU[1] += m_selfCarSpeed[1];
	objectSpeedInCarBackRFU[2] += m_selfCarSpeed[2];
	return std::move(objectSpeedInCarBackRFU);
}