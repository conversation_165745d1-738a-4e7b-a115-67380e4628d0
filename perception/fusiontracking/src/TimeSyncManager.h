/*
 * @Author: AI Assistant
 * @Date: 2025-01-03
 * @Description: 车路融合时间同步管理器
 * 用于优化车端lidar跟踪目标与路端基站lidar跟踪目标的时间同步
 */

#ifndef TIME_SYNC_MANAGER_H
#define TIME_SYNC_MANAGER_H

#include <vector>
#include <map>
#include <memory>
#include <string>
#include <fstream>
#include <cmath>
#include <algorithm>
#include <ros/ros.h>
#include <spdlog/spdlog.h>

/**
 * @brief 时间同步管理器类
 * 
 * 主要功能：
 * 1. 动态计算车端与路端的时间偏移
 * 2. 自适应学习和调整时间同步参数
 * 3. 网络延迟估计和补偿
 * 4. 数据包管理和完整性检查
 * 5. 时间同步质量监控和统计
 */
class TimeSyncManager {
public:
    /**
     * @brief 时间同步参数结构体
     */
    struct TimeSyncConfig {
        // 基础参数
        float base_offset_ms = 360.0f;           // 基础时间偏移(ms)
        float frame_duration_ms = 100.0f;        // 帧持续时间(ms)
        float max_time_gap_ms = 400.0f;          // 最大允许时间差(ms)
        
        // 自适应参数
        bool enable_adaptive = true;             // 是否启用自适应
        float adaptation_rate = 0.1f;            // 自适应学习率
        size_t history_size = 50;                // 历史记录大小
        float outlier_threshold = 3.0f;          // 异常值阈值
        float max_dynamic_offset_ms = 200.0f;    // 最大动态偏移
        
        // 网络延迟估计
        bool enable_network_delay_estimation = true;
        float network_delay_smoothing = 0.3f;
        float max_network_delay_ms = 500.0f;
        float packet_timeout_ms = 5000.0f;
        
        // 数据包管理
        bool enable_packet_management = true;
        size_t packet_buffer_size = 100;
        float incomplete_packet_timeout_ms = 1000.0f;
        
        // 调试和日志
        bool enable_debug_logging = true;
        int log_interval = 10;
        bool save_sync_statistics = false;
        std::string statistics_file_path = "/tmp/time_sync_stats.csv";
        
        // 质量控制
        int min_data_points_for_adaptation = 10;
        int stability_check_window = 20;
        float max_std_deviation_ms = 50.0f;
        
        // 特殊场景处理
        bool handle_packet_loss = true;
        bool handle_burst_traffic = true;
        float emergency_fallback_offset_ms = 360.0f;
    };

    /**
     * @brief 数据包信息结构体
     */
    struct PacketInfo {
        float timestamp;                         // 数据包时间戳
        float receive_time;                      // 接收时间
        int packet_id;                           // 包ID
        int total_packets;                       // 总包数
        bool is_complete;                        // 是否完整接收
        float processing_delay;                  // 处理延迟
    };

    /**
     * @brief 时间同步统计信息
     */
    struct SyncStatistics {
        float avg_time_gap = 0.0f;               // 平均时间差
        float std_time_gap = 0.0f;               // 时间差标准差
        float current_dynamic_offset = 0.0f;     // 当前动态偏移
        float estimated_network_delay = 0.0f;    // 估计网络延迟
        int total_packets = 0;                   // 总包数
        int outlier_packets = 0;                 // 异常包数
        float sync_quality_score = 0.0f;         // 同步质量评分
        bool is_stable = false;                  // 是否稳定
    };

public:
    /**
     * @brief 构造函数
     */
    TimeSyncManager();
    
    /**
     * @brief 析构函数
     */
    ~TimeSyncManager();

    /**
     * @brief 初始化时间同步管理器
     * @param config_file_path 配置文件路径
     * @param logger 日志记录器
     * @return 是否初始化成功
     */
    bool initialize(const std::string& config_file_path, 
                   std::shared_ptr<spdlog::logger> logger = nullptr);

    /**
     * @brief 计算最优时间偏移
     * @param vehicle_timestamp 车端时间戳(ms)
     * @param roadside_timestamp 路端时间戳(ms)
     * @return 最优时间偏移(ms)
     */
    float calculateOptimalOffset(float vehicle_timestamp, float roadside_timestamp);

    /**
     * @brief 更新数据包信息
     * @param timestamp 数据包时间戳
     * @param receive_time 接收时间
     * @param packet_id 包ID
     * @param total_packets 总包数
     * @param is_complete 是否完整
     */
    void updatePacketInfo(float timestamp, float receive_time, 
                         int packet_id = 0, int total_packets = 1, bool is_complete = true);

    /**
     * @brief 获取当前同步统计信息
     * @return 同步统计信息
     */
    SyncStatistics getSyncStatistics() const;

    /**
     * @brief 检查时间同步质量
     * @return 同步质量是否良好
     */
    bool isSyncQualityGood() const;

    /**
     * @brief 重置时间同步状态
     */
    void reset();

    /**
     * @brief 保存同步统计数据到文件
     * @param file_path 文件路径
     * @return 是否保存成功
     */
    bool saveSyncStatistics(const std::string& file_path = "") const;

private:
    /**
     * @brief 加载配置文件
     * @param config_file_path 配置文件路径
     * @return 是否加载成功
     */
    bool loadConfig(const std::string& config_file_path);

    /**
     * @brief 更新时间差统计
     * @param time_gap 时间差
     */
    void updateTimeGapStatistics(float time_gap);

    /**
     * @brief 检查是否为异常值
     * @param time_gap 时间差
     * @return 是否为异常值
     */
    bool isOutlier(float time_gap) const;

    /**
     * @brief 计算自适应偏移
     * @return 自适应偏移值
     */
    float calculateAdaptiveOffset();

    /**
     * @brief 估计网络延迟
     * @return 网络延迟估计值
     */
    float estimateNetworkDelay();

    /**
     * @brief 管理数据包缓存
     */
    void managePacketBuffer();

    /**
     * @brief 计算同步质量评分
     * @return 质量评分(0-1)
     */
    float calculateSyncQualityScore() const;

    /**
     * @brief 检查同步稳定性
     * @return 是否稳定
     */
    bool checkSyncStability() const;

private:
    TimeSyncConfig m_config;                     // 配置参数
    std::shared_ptr<spdlog::logger> m_logger;    // 日志记录器
    
    // 时间同步状态
    std::vector<float> m_time_gap_history;       // 时间差历史
    float m_dynamic_offset;                      // 动态偏移
    float m_network_delay;                       // 网络延迟
    
    // 数据包管理
    std::map<float, std::vector<PacketInfo>> m_packet_buffer;
    
    // 统计信息
    SyncStatistics m_statistics;
    int m_frame_count;                           // 帧计数
    std::ofstream m_stats_file;                  // 统计文件流
    
    // 内部状态
    bool m_is_initialized;                       // 是否已初始化
    ros::Time m_last_update_time;                // 上次更新时间
};

#endif // TIME_SYNC_MANAGER_H
