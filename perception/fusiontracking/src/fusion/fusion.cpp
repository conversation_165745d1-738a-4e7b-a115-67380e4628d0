/********************************************************************************
* @author: shuang<PERSON><PERSON> han
* @date: 2022/8/9 下午5:11
* @version: 1.0
* @description:
********************************************************************************/

#include <fusion.h>

#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include "fusiontracking/sort/getiou.h"

using namespace std;


//lidar-radar融合，关联矩阵lidar为行、radar为列
void FusionNode::fusionLidarAndRadar(common_msgs::sensorobjects1& lidar_objs) {
	pcl::PointCloud<pcl::PointXYZI> radarObjectCenterPointsInLidarAxis;     //毫米波雷达检测的目标点云(lidar坐标系下）
	deque<int> found_index;
	//lidar + radar 融合
	std::cout<<"\t融合lidar radar的检测结果...，当前radar时间 = "<<radar_objects[0].timestamp / 1000.0<<std::endl;
	
	
	//TODO  初值为0？
	cout<<"关联矩阵大小： raw = "<<lidar_objs.obs.size()<<",col = "<< radar_objects[0].obs.size()<<endl;
	vector<vector<double>> lidarRadarDistanceMatrix(lidar_objs.obs.size(),vector<double>( radar_objects[0].obs.size(),0));
	
	for(int i = 0; i < lidar_objs.obs.size(); i++)//在lidar目标检测回调函数中已经对宽长为0或类别为其他的目标进行了剔除，此处不需再处理
	{
		
		auto sigleLidarObject = lidar_objs.obs[i];
		for(int j = 0; j < radar_objects[0].obs.size(); j++)//在lidar目标检测回调函数中已经对宽长为0或类别为其他的目标进行了剔除，此处不需再处理
		{
			//radar分类：00:point,01:car,02:trunk,03:pedestrain,04:motecycle,05:bicycle,06:wide target 07:reserved
			if( radar_objects[0].obs[j].classification == 0 || radar_objects[0].obs[j].classification == 7)//radar分类为0的点，多而乱
				continue;
			
			
			//step2 使用距离损失矩阵+匈牙利匹配找匹配的目标
			//step2.1 计算距离损失矩阵，行是radar检测目标个数，列是lidar检测目标个数
			pcl::PointXYZI pointsingle;
			pointsingle.x = radar_objects[0].obs[j].x;
			pointsingle.y = radar_objects[0].obs[j].y;
			pointsingle.z = 0;
			TransForm(pointsingle);//radar转到lidar坐标系
			//TODO 距离太远的radar点不参与计算
			pointsingle.intensity = radar_objects[0].obs[j].classification * 40;
			radarObjectCenterPointsInLidarAxis.push_back(pointsingle);
			
			pcl::PointXYZI pointlidar;
			pointlidar.x = lidar_objs.obs[i].x;
			pointlidar.y = lidar_objs.obs[i].y;
			pointlidar.z = lidar_objs.obs[i].z;
			float lidarCenter2RadarDistance = Distance(pointsingle, pointlidar);//求取两个目标的距离
			
			if(lidarCenter2RadarDistance < lidarRadarDistanceThro_)//中心点距离在3.5米内认为是同一目标
				lidarRadarDistanceMatrix[i][j] = lidarCenter2RadarDistance;
		}
	}
	
	
	
	//step2.2 匈牙利匹配找匹配的目标
	HungarianAlgorithm HungAlgo;
	vector<int> assignment;
	HungAlgo.Solve(lidarRadarDistanceMatrix,assignment);
	/////验证用
	//cout<<"validation：\n";
	//for (int i = 0; i < lidarRadarDistanceMatrix.size(); ++i) {
	//	for (int j = 0; j < lidarRadarDistanceMatrix[i].size(); ++j) {
	//		cout<<lidarRadarDistanceMatrix[i][j]<<" ";
	//	}
	//	cout<<endl;
	//}
	cout<<"lidar-radar validation：assigenment\n";
	for (int j = 0; j < assignment.size(); ++j) {
		cout<<assignment[j]<<" ";
	}
	cout<<endl;
	/////验证用
	
	pcl::PointCloud<pcl::PointXYZI> lidarRadarFusionCenterPointCloud; //存储激光点云检测目标的中心点 + 和毫米波雷达检测的目标点云
	//step2.3 根据匹配结果用radar检测目标的速度更新lidar检测目标的速度
	for(int i = 0; i < lidar_objs.obs.size(); i++){
		if(assignment[i] >= 0 && lidar_objs.obs[i].x >= 0 ){//radar检测目标的个数为assignment的大小 && lidar后向的障碍物不要，因为没有radar点 && lidarRadarDistanceMatrix[i][assignment[i]] < 3.0
			lidar_objs.obs[i].relspeedx = radar_objects[0].obs[assignment[i]].relspeedx;
			lidar_objs.obs[i].relspeedy = radar_objects[0].obs[assignment[i]].relspeedy;
		}
	}
	
	/////验证用
	//for(int i = 0; i < lidar_objs.obs.size(); i++){
	//	if(lidar_objs.obs[i].speedSource == 2){//
	//		pcl::PointXYZI lidarRadarFusionPoint;
	//		lidarRadarFusionPoint.x = lidar_objs.obs[i].x;
	//		lidarRadarFusionPoint.y = lidar_objs.obs[i].y;
	//		lidarRadarFusionPoint.z = lidar_objs.obs[i].z;
	//		lidarRadarFusionPoint.intensity = lidar_objs.obs[i].speedSource;//10
	//		lidarRadarFusionCenterPointCloud.push_back(lidarRadarFusionPoint);
	//	}
	//}
	//
	////publish test cloud 激光点云检测目标的中心点 + 和毫米波雷达检测的目标点云
	//cout<<"fusion point size = "<<lidarRadarFusionCenterPointCloud.points.size()<<endl;
	//sensor_msgs::PointCloud2 lidarRadarFusionCenterPointCloudMsg;
	//pcl::toROSMsg(lidarRadarFusionCenterPointCloud, lidarRadarFusionCenterPointCloudMsg);
	//lidarRadarFusionCenterPointCloudMsg.header.frame_id = "velodyne";
	//pub_LidarRadarFusionObjs_PCshow_.publish(lidarRadarFusionCenterPointCloudMsg);
	///////验证用
	
	
	//发布lidar坐标系下的radar数据，转为了点
	sensor_msgs::PointCloud2 msg2;
	pcl::toROSMsg(radarObjectCenterPointsInLidarAxis, msg2);
	msg2.header.frame_id = "velodyne";
	pub_RadarObjectsPointCloud.publish (msg2);
	
}


/***
 * 点云投影到相机
 * @param lidar_objects
 * @param camera_objects
 */
bool FusionNode::objectProjection(common_msgs::sensorobjects1 &lidar_objects, common_msgs::sensorobjects &camera_objects,
                         deque<sensor_msgs::CompressedImage>& cameraPictureDeque, sensor_msgs::ImagePtr& cameraMsg,
                         pcl::PointCloud<pcl::PointXYZI>::Ptr lidarFuseCameraPointCloud) {
	std::vector<cv::Point3f> pts_3d;
	//读取雷达的检测目标的中心点
	vector<int> index;//建立索引向量
	
	
	vector<Box> allObjectLeftTop_RightBottomPoints;//存入每个目标8角点投影后得到的左上右下两个角点
	vector<std::vector<cv::Point2f>> allObject8CornerPoints;//存入每个目标8角点投影后得到的点
	for (int i = 0; i < lidar_objects.obs.size(); i++) {
		
		if (lidar_objects.obs[i].x > 2.0)// 0.64 lidar及lidar检测X前
		{
			//剔除宽长为0的错误目标
			if (lidar_objects.obs[i].width == 0 || lidar_objects.obs[i].length == 0)
				continue;
			
			vector<common_msgs::point3d> oneObjectPointCorners = boxes_to_corners_3d(lidar_objects.obs[i]);//存放一个目标的8角点
			
			pts_3d.emplace_back(cv::Point3f(lidar_objects.obs[i].x, lidar_objects.obs[i].y, lidar_objects.obs[i].z));
			index.push_back(i);
			
			
			//8角点转换OpenCV格式
			std::vector<cv::Point3f> oneObjectPointCornersVector;
			for (int j = 0; j < oneObjectPointCorners.size(); ++j) {
				cv::Point3f point3F{oneObjectPointCorners[j].x, oneObjectPointCorners[j].y, oneObjectPointCorners[j].z};
				oneObjectPointCornersVector.emplace_back(point3F);
			}
			
			//8角点投影
			std::vector<cv::Point2f> pts_2d_corner;
			if (oneObjectPointCornersVector.size() > 0){
				cv::projectPoints(oneObjectPointCornersVector, rotationVector_, translationVector_, camera_matrix_,
				                  distortion_coeff_,
				                  pts_2d_corner);
			}
			allObject8CornerPoints.emplace_back(pts_2d_corner);
			if (!pts_2d_corner.empty()){
				
				//投影的8角点找到左上角和右下角点
				double min_x, max_x, min_y, max_y;
				min_x = pts_2d_corner[0].x;
				max_x = pts_2d_corner[0].x;
				min_y = pts_2d_corner[0].y;
				max_y = pts_2d_corner[0].y;
				for (int j = 1; j < pts_2d_corner.size(); ++j) {
					if (pts_2d_corner[j].x < min_x)
						min_x = pts_2d_corner[j].x;
					if (pts_2d_corner[j].x > max_x)
						max_x = pts_2d_corner[j].x;
					if (pts_2d_corner[j].y < min_y)
						min_y = pts_2d_corner[j].y;
					if (pts_2d_corner[j].y > max_y)
						max_y = pts_2d_corner[j].y;
					
				}
				//边界检查,方便检查框，横向减10像素，纵向减5像素
				min_x = min_x < 0 ? 10 : min_x;
				max_x = max_x > 1920 ? 1920 - 10 : max_x;
				min_y = min_y < 0 ? 10 : min_y;
				max_y = max_y > 1080 ? 1080 - 10 : max_y;
				
				Box lidarObjectBox{min_x, min_y, max_x, max_y};
				allObjectLeftTop_RightBottomPoints.emplace_back(lidarObjectBox);//每个目标存左上右下两个角点
			}
			else {
				cout << "WARN：8角点投影为空\n";
			}
		}
		
	}
	
	//相机检测4角点
	vector<Box> cameraBoxCorner;//一帧相机检测目标的8角点
	for (auto cornerPoint: camera_objects.obs) {
		float leftTopX = cornerPoint.x;
		float leftTopY = cornerPoint.y;
		float rightBottomX = cornerPoint.x + cornerPoint.width;
		float rightBottomY = cornerPoint.y + cornerPoint.height;
		
		Box cameraObjectBox{leftTopX, leftTopY, rightBottomX, rightBottomY};
		cameraBoxCorner.emplace_back(cameraObjectBox);
	}
	
	//3D框中心点
	std::vector<cv::Point2f> pts_2d; //3D框中心点点云投影到图像后的像素值
	if (pts_3d.size() > 0){
		
		cv::projectPoints(pts_3d, rotationVector_, translationVector_, camera_matrix_, distortion_coeff_,
		                  pts_2d);//opencv投影函数
	}
	else {
		cout << "WARN : 小车前向没有3D点，投影失败\n";
		return false;
	}
	
	cout << "\tlidar X轴2.0米外的点云数量： " << pts_3d.size() << ",投影后的尺寸为: " << pts_2d.size() << endl;
	
	//step2 使用距离损失矩阵+匈牙利匹配找匹配的目标
	//step2.1 计算距离损失矩阵，行是lidar检测目标个数，列是camera检测目标个数
	cout << "matrix size :" << pts_2d.size() << ", " << camera_objects.obs.size() << std::endl;
	//TODO  初值为0？
	vector<vector<double>> distanceMatrix(pts_2d.size(), vector<double>(camera_objects.obs.size(), 0));
	for (int i = 0; i < pts_2d.size(); i++) {
		cv::Point2f lidarObject2D = pts_2d[i];
		for (int j = 0; j < camera_objects.obs.size(); j++) {
			
			//iou 计算有nan值
			double iou = IoU_compute(allObjectLeftTop_RightBottomPoints[i], cameraBoxCorner[j]);//iouProcess取 1与IOU的差值
			iou = isnan(iou) ? 0 : iou;
			double iouProcess = 1 - iou;
			
			float lidarObject2DLength = allObjectLeftTop_RightBottomPoints[i].y1 - allObjectLeftTop_RightBottomPoints[i].y0;
			float lidarObject2DWidth = allObjectLeftTop_RightBottomPoints[i].x1 - allObjectLeftTop_RightBottomPoints[i].x0;
			float lengthRatio = lidarObject2DLength / (float) (camera_objects.obs[j].height + 1e-6);
			float widthRatio = lidarObject2DWidth/ (float) (camera_objects.obs[j].width + 1e-6);
			
			// 不考虑长宽差距过大的目标
			if (lengthRatio < 0.5 || lengthRatio > 2.0
			        || widthRatio < 0.5 || widthRatio > 2.0)
			{
				iouProcess = 0.9;//IOU 0.2
			}
			
			common_msgs::sensorobject cameraObject2D = camera_objects.obs[j];
			//计算像素欧式距离
			double distance = sqrt(
					pow(lidarObject2D.x - cameraObject2D.x, 2) + pow(lidarObject2D.y - cameraObject2D.y, 2));
			
			//cout << i << " : iou caculate: " << iou << ", process iou: " << iouProcess << std::endl;
			double weight = (1 - lidarCameraIOUWeight_) * distance + lidarCameraIOUWeight_ * iouProcess;
			
			//Manhattan Distance 曼哈顿距离
			double manhattanDistanceX = abs(lidarObject2D.x - cameraObject2D.x);
			if (manhattanDistanceX > (camera_objects.obs[j].width) || manhattanDistanceX > (lidarObject2DWidth)){
				iouProcess = 1.0;
			}
			
			// 8角点xy最大最小值的矩形框与相机检测2D框的IOU的处理值：1-IOU
			distanceMatrix[i][j] = iouProcess;
			
		}
	}
	cout << "caculate matrix finish" << std::endl;
	//step2.2 匈牙利匹配找匹配的目标
	HungarianAlgorithm HungAlgo;
	vector<int> assignment;
	HungAlgo.Solve(distanceMatrix, assignment);
	cout << "caculate HungAlgo finish" << std::endl;
	
	
	// 干掉IOU为0的匹配对，不进行匹配
	for (int i = 0; i < assignment.size(); i++)
	{
		if (assignment[i] == -1)
		{
			continue;
		}
		
		if (distanceMatrix[i][assignment[i]] > 0.93) //经验调节：IOU小于0.01或者
		{
			assignment[i] = -1;
		}
		
	}
	
	
	vector<vector<float>> allMatchInfo;
	//step2.3 根据匹配结果用camera检测目标的类别更新lidar检测目标的类别
	std::vector<cv::Point2f> pts_2dFindObjectInPicture; //点云投影到图像后的匹配的像素值
	for(int i = 0; i < pts_2d.size(); i++){
		//if(pts_2d[i].x > imageMaxX_ || pts_2d[i].x < 0 || pts_2d[i].y > imageMaxY_ || pts_2d[i].y < 0)
		//	continue;//跳过超出边界的像素
		
		if(assignment[i] >= 0){
			pts_2dFindObjectInPicture.push_back(pts_2d[i]);
			lidar_objects.obs[(int) index.at(i)].classification = camera_objects.obs[assignment[i]].classification;//享智分类=相机分类
			
			//lidar 2D索引，相机目标索引，lidar目标的中心点x，lidar目标的中心点y,相机目标左上角点x,相机目标左上角点y
			vector<float> singleMatchInfo{(float) i, (float) assignment[i], pts_2d[i].x, pts_2d[i].y, camera_objects.obs[assignment[i]].x, camera_objects.obs[assignment[i]].y};
			cout<<"lidar-camera index info :\nlidarIndex="<<i<<", cameraIndex="<<assignment[i]<<",lidar positionXY="<<pts_2d[i].x<<", "<< pts_2d[i].y<<", camera positionXY="<<
			     camera_objects.obs[assignment[i]].x<<", "<<camera_objects.obs[assignment[i]].y<<", IOU:"<<1 - distanceMatrix[i][assignment[i]]<<
				 ",\n height ratio="<<(allObjectLeftTop_RightBottomPoints[i].y1 - allObjectLeftTop_RightBottomPoints[i].y0 ) / (float) (camera_objects.obs[assignment[i]].height + 1e-6)<<
				 ", width ratio="<<(allObjectLeftTop_RightBottomPoints[i].x1 - allObjectLeftTop_RightBottomPoints[i].x0 )/ (float) (camera_objects.obs[assignment[i]].width + 1e-6)<<
				 ",\nlidar point width length: "<<allObjectLeftTop_RightBottomPoints[i].y1<< ", "<<allObjectLeftTop_RightBottomPoints[i].y0 <<
				 ",\ncamera point width length: "<<(float) (camera_objects.obs[assignment[i]].height )<<", "<<(float) (camera_objects.obs[assignment[i]].width)<<
				 ",\nlidar 3D point: "<<lidar_objects.obs[(int) index.at(i)].x<<", "<<lidar_objects.obs[(int) index.at(i)].y<<endl<<endl;//xxx与跟踪的RVIZ的点云朝向不同，这里X是朝前，跟踪中RVIZX朝右
			
			
			allMatchInfo.emplace_back(singleMatchInfo);
		}
	}
	
	bool checkFlag = false;
	//用于显示验证
	if(fusionValidationFlag_){
		checkFlag = projectPoint2Image(cameraPictureDeque, camera_objects, pts_2dFindObjectInPicture,
									   cameraMsg,allObjectLeftTop_RightBottomPoints,allObject8CornerPoints,allMatchInfo);
		cout<<"caculate projectPoint2Image finish"<<std::endl;
	}
	
	cout<<"objectProjection finish"<<std::endl;
	if(!checkFlag){
		std::cout<<"WARN: not find suit lidar objects points in picture\n";
		return false;
	}
	else
		return true;
}



void FusionNode::fusionLidarAndCamera(common_msgs::sensorobjects1& lidar_objs){
	//相机雷达融合
		std::cout<<"\t融合lidar camera的检测结果..."<<"，当前camera时间："<<camera_objects[0].timestamp / 1000.0 <<"\n";
		pcl::PointCloud<pcl::PointXYZI>::Ptr lidarFuseCameraPointCloud(new pcl::PointCloud<pcl::PointXYZI>);
		sensor_msgs::ImagePtr cameraMsg(new sensor_msgs::Image);
		TicToc ticTocLidarCamerastart;
		bool lidarFuseCameraFlag = objectProjection(lidar_objs, camera_objects[0],
		                                               cameraPictureDeque,cameraMsg,
		                                               lidarFuseCameraPointCloud);
		cout<<"fusion lidar-camera start用时："<<endl;
		ticTocLidarCamerastart.toc();
	
		if(lidarFuseCameraPointCloud != nullptr){//相机检测到目标，即lidar与相机目标融合成功
			//	发布点云
			sensor_msgs::PointCloud2 lidarFuseCameraPointCloudMsg;
			pcl::toROSMsg(*lidarFuseCameraPointCloud,lidarFuseCameraPointCloudMsg);
			//lidarFuseCameraPointCloudMsg.header.frame_id = "velodyne";
			lidarFuseCameraPointCloudMsg.header = lidar_objs.header;
			pub_lidarFuseCameraObjectPointForRVIZ.publish(lidarFuseCameraPointCloudMsg);
		}
		//if(lidarFuseCameraFlag){// 只在lidar、camera融合后发布
		//lidar的2D点与图像二维检测框验证
		cameraMsg->header = lidar_objs.header;
		pub_lidarObjectInPictureForRVIZ.publish(cameraMsg);
		//}
	
	
}

//发布添加运动信息的融合结果信息
void  FusionNode::publishFusionObjectsToTracker(common_msgs::sensorobjects1& lidar_objs){
	common_msgs::fusionObjects lidar_objsToTracker;
	lidar_objsToTracker.header = lidar_objs.header;
	lidar_objsToTracker.isvalid = 1;
	lidar_objsToTracker.timestamp = lidar_objs.header.stamp.toSec() * 1000;
	lidar_objsToTracker.gpstime = lidar_objs.header.stamp.toSec() * 1000;//TODO 验证

	for(int i = 0; i < lidar_objs.obs.size(); i++)
	{
		//去除小车车体上不为误检成的目标
		float center_x_dev = lidar_objs.obs[i].y;
		float center_y_dev = lidar_objs.obs[i].x;
		float center_x_y_dev = sqrt(center_x_dev * center_x_dev + center_y_dev * center_y_dev);
		if(center_x_y_dev < 1){
			continue;
		}
		common_msgs::fusionObject obj;
		obj.id = lidar_objs.obs[i].id;
		obj.x = -lidar_objs.obs[i].y;//绕Z顺时针转90度：X朝前转到Y朝前
		obj.y = lidar_objs.obs[i].x;
		obj.relspeedy = lidar_objs.obs[i].relspeedx;
		obj.relspeedx = -lidar_objs.obs[i].relspeedy;//速度转换 速度来源于radar
		obj.azimuth = lidar_objs.obs[i].rt;
		obj.width = lidar_objs.obs[i].width;
		obj.length = lidar_objs.obs[i].length;
		obj.height = lidar_objs.obs[i].height;
		obj.classification = lidar_objs.obs[i].classification;
		obj.value = lidar_objs.obs[i].value;
		
		vector<common_msgs::point3d> pointcorners = boxes_to_corners_3d(lidar_objs.obs[i]);//转成8角点
		for (int k = 0; k < pointcorners.size(); k++)
		{
			obj.points.push_back(pointcorners[k]);
		}
		
		//TODO 需要知道自车速度加以判断 {"car", "bic"自行车, "bus", "tri"三轮车, "ped", "cone"路锥, "tru", "unk"未知};根据跟踪结果再次对动静态目标进行判断
		if(lidar_objs.obs[i].classification == 5){//lidar检测结果类别是路锥 //20220902 hsq 20220908 更改静止目标为3
			obj.motionInfo = 3;//4.静止目标（路锥）
		}
		else{
			if(abs(lidar_objs.obs[i].relspeedx - motionThreshold_) >= 0 || abs(lidar_objs.obs[i].relspeedy - motionThreshold_) >= 0)
				obj.motionInfo = 1;//1.动态目标 2.静态目标（静止的动态目标）
			else
				obj.motionInfo = 2;
		}
		if(obj.relspeedx != 0 || obj.relspeedy != 0){
			obj.speedSource = 2;// radar
		}
		
		lidar_objsToTracker.obs.push_back(obj);
	}
	pub_FusionObjectsToTracker_.publish(lidar_objsToTracker);//common_msgs::fusionOnjects>("objectsFusionToTacker");//发布融合后目标-用新的msgs传给跟踪20220902
	
	visualization(lidar_objsToTracker);//可视化融合目标
}

void FusionNode::publishFusionObjects(common_msgs::sensorobjects1& lidar_objs){
	common_msgs::sensorobjects lidar_objs_source; //融合后的目标
	lidar_objs_source.timestamp = lidar_objs.header.stamp.toSec() * 1000;
	lidar_objs_source.isvalid = 1;
	for(int i = 0; i < lidar_objs.obs.size(); i++)
	{
		//去除小车车体上不为误检成的目标
		float center_x_dev = lidar_objs.obs[i].y;
		float center_y_dev = lidar_objs.obs[i].x;
		float center_x_y_dev = sqrt(center_x_dev * center_x_dev + center_y_dev * center_y_dev);
		if(center_x_y_dev < 1)
			continue;
		common_msgs::sensorobject obj;
		obj.x = -lidar_objs.obs[i].y;//绕Z顺时针转90度：X朝前转到Y朝前
		obj.y = lidar_objs.obs[i].x;
		obj.width = lidar_objs.obs[i].width;
		obj.length = lidar_objs.obs[i].length;
		obj.height = lidar_objs.obs[i].height;
		obj.azimuth = lidar_objs.obs[i].rt;
		obj.classification = lidar_objs.obs[i].classification;
		obj.id = lidar_objs.obs[i].id;
		obj.value = lidar_objs.obs[i].value;
		obj.relspeedx = -lidar_objs.obs[i].relspeedy;//速度转换 速度来源于radar
		obj.relspeedy = lidar_objs.obs[i].relspeedx;
		
		
		vector<common_msgs::point3d> pointcorners = boxes_to_corners_3d(lidar_objs.obs[i]);//转成8角点
		for (int k = 0; k < pointcorners.size(); k++)
		{
			obj.points.push_back(pointcorners[k]);
		}
		lidar_objs_source.obs.push_back(obj);
		
	}
	cout << "\t融合后检测目标的数量： " << lidar_objs_source.obs.size() << endl;
	pub_objects_source.publish(lidar_objs_source);//发布融合后的目标 <common_msgs::sensorobjects>("objectfusion")
	visualization(lidar_objs_source);//可视化融合目标
}

//将common_msgs::sensorobjects1转成包含速度来源和动静态目标信息的msg


void FusionNode::fusionAllSensorResult() {
	TicToc ticToc;
	
	if(lidar_objects_.empty())
		return;
	
	static int lidarDectionFrameCount = 1;
	cout << "第 "<<lidarDectionFrameCount++<<" 帧lidar检测结果................................................. "  << endl;
	
	common_msgs::sensorobjects1 lidar_objs;
	{
		std::mutex lidarObjectMutex;
		std::lock_guard<std::mutex> lockLidarObject(lidarObjectMutex);
		lidar_objs = lidar_objects_.front();
		lidar_objects_.pop_front();
	}
	double curLidarStamp = lidar_objs.header.stamp.toSec();
	
	bool key_cam = true;
	bool key_radar = true;
	
	//lidar radar 时间同步： 保证radar容器内的第一帧是与lidar最近的一帧
	{
		std::mutex lock_radar;
		std::lock_guard<std::mutex> lockRadar(lock_radar);
		int curRadarIndex = 0;
		for (int i = 0; i < radar_objects.size(); ++i) {
			if(radar_objects[i].timestamp / 1000.0 > curLidarStamp)//TODO bug 从i开始
				break;
			curRadarIndex = i;
		}
		if(curRadarIndex > 0){//小于0 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			if(abs(curLidarStamp - radar_objects[curRadarIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curLidarStamp - radar_objects[curRadarIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curRadarIndex += 1;//取最近的一帧数据
			}
			while(curRadarIndex--){
				radar_objects.pop_front();
			}
		}
		
	}
	//lidar camera 时间同步：保证camera容器内的第一帧是与lidar最近的一帧
	{
		std::mutex cameraObjectMutex;
		std::lock_guard<std::mutex> cameraLock(cameraObjectMutex);
		int curCameraIndex = 0;
		for (int i = 0; i < camera_objects.size(); ++i) {
			if(camera_objects[i].timestamp / 1000.0 > curLidarStamp)//TODO bug 从i开始
				break;
			curCameraIndex = i;
		}
		if(curCameraIndex > 0){//小于0 说明容器内第一帧camera数据就大于当前帧lidar数据，不处理
			if (abs(curLidarStamp - camera_objects[curCameraIndex].timestamp / 1000.0) >//lidar前一帧
			    abs(curLidarStamp - camera_objects[curCameraIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curCameraIndex += 1;//取最近的一帧数据
			}
			while (curCameraIndex--) {
				camera_objects.pop_front();
			}
		}
		
	}
	
	cout<<std::setprecision(16)<<"\t当前lidar帧检测时间 = "<<curLidarStamp<<", lidar检测结果数 = "<<lidar_objs.obs.size()<<std::endl;
	
	
	//相机和毫米波雷达的检测目标都有数据
	if(radar_objects.size() > 0 && camera_objects.size() > 0)
	{
		cout<<"\tradar第一帧时间差 = "<<curLidarStamp - radar_objects[0].timestamp / 1000.0<<
		    ", camera第一帧时间差 = "<<curLidarStamp - camera_objects[0].timestamp / 1000.0<<endl;
		cout<<"\tcamera detection size = "<<camera_objects[0].obs.size()<<endl;
		cout<<"\tradar detection size = "<<radar_objects[0].obs.size()<<endl;
		TicToc ticToc;
		fusionLidarAndRadar(lidar_objs);
		cout<<"fusion lidar radar用时："<<endl;
		ticToc.toc();
		
		TicToc ticTocLidarCamera;
		fusionLidarAndCamera(lidar_objs);
		cout<<"fusion lidar-camera用时："<<endl;
		ticTocLidarCamera.toc();
		
		
		key_cam = false;//cam有数据跳过下边radar与camera融合
		key_radar = false;//radar有数据跳过下边radar与camera融合
	}
	
	//只有相机数据，融合lidar和相机
	if(radar_objects.size() <= 0 && camera_objects.size() > 0 && key_cam)
	{
		//相机雷达融合
		////if(abs(camera_objects[0].timestamp / 1000.0 - lidar_objs.header.stamp.toSec()) < 0.3)
		////{
			cout<<"\tcamera第一帧时间差 = "<<curLidarStamp - camera_objects[0].timestamp / 1000.0<<endl;
			cout<<"\tcamera detection size = "<<camera_objects[0].obs.size()<<endl;
			cout<<"\t只有相机数据：融合lidar camera..."<<endl;
		//	pcl::PointCloud<pcl::PointXYZI>::Ptr lidarFuseCameraPointCloud(new pcl::PointCloud<pcl::PointXYZI>);
		//	cloud_projectionone(lidar_objs, camera_objects[0],lidarFuseCameraPointCloud);
		//	std::mutex cameraObjectMutex;
		//	std::lock_guard<std::mutex> cameraLock(cameraObjectMutex);
		//	camera_objects.pop_front();
		////}
		
		fusionLidarAndCamera(lidar_objs);
	}
	
	//只有radar数据
	if(radar_objects.size() > 0 && camera_objects.size() <= 0 && key_radar)//有radar、lidar数据，没有相机数据
	{
		cout<<"\tradar第一帧时间差 = "<<curLidarStamp - radar_objects[0].timestamp / 1000.0<<endl;
		cout<<"\tradar detection size = "<<radar_objects[0].obs.size()<<endl;
		cout<<"\t只有radar数据：融合radar lidar..."<<endl;
		fusionLidarAndRadar(lidar_objs);
	}
	pub_FusionObjects.publish(lidar_objs);//发布融合后的目标 - apollo小车msg common_msgs::sensorobjects1 "objects"
	
	publishFusionObjects(lidar_objs);// 发布融合后的目标 - 享智msg 绕Z顺时针转90度：X朝前转到Y朝前，包含8角点
	publishFusionObjectsToTracker(lidar_objs);// 发布融合后的目标 - 享智msg 绕Z顺时针转90度：X朝前转到Y朝前，包含8角点,加入运动属性
	cout<<"fusion用时："<<endl;
	ticToc.toc();
}

//显示
void FusionNode::visualization(const common_msgs::sensorobjects& lidar_objs_source){
	////////////////////////
	visualization_msgs::MarkerArray MarkerArray;
	for (size_t i = 0; i < lidar_objs_source.obs.size(); i++)
	{
		visualization_msgs::Marker line_list;
		line_list.header.frame_id  = "/velodyne";
		line_list.header.stamp  = ros::Time::now();//TODO  lidar时间戳
		line_list.ns = "points_and_lines";
		line_list.lifetime =ros::Duration(0.1);
		line_list.action = visualization_msgs::Marker::ADD;
		line_list.pose.orientation.w = 1.0;
		line_list.id = i;
		line_list.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list.scale.x = 0.2;
		//line_list.scale.y = 0.1;
		//line_list.scale.z = 0.1;
		//color green
		line_list.color.a = 1;
		line_list.color.r = 0;
		line_list.color.g = 1;
		line_list.color.b = 0;
		
		geometry_msgs::Point p0, p1, p2, p3;
		geometry_msgs::Point p4, p5, p6, p7;
		
		p0.x = lidar_objs_source.obs[i].points[0].x;
		p0.y = lidar_objs_source.obs[i].points[0].y;
		p0.z = lidar_objs_source.obs[i].points[0].z;
		
		p1.x = lidar_objs_source.obs[i].points[1].x;
		p1.y = lidar_objs_source.obs[i].points[1].y;
		p1.z = lidar_objs_source.obs[i].points[1].z;
		
		p2.x = lidar_objs_source.obs[i].points[2].x;
		p2.y = lidar_objs_source.obs[i].points[2].y;
		p2.z = lidar_objs_source.obs[i].points[2].z;
		
		p3.x = lidar_objs_source.obs[i].points[3].x;
		p3.y = lidar_objs_source.obs[i].points[3].y;
		p3.z = lidar_objs_source.obs[i].points[3].z;
		
		p4.x = lidar_objs_source.obs[i].points[4].x;
		p4.y = lidar_objs_source.obs[i].points[4].y;
		p4.z = lidar_objs_source.obs[i].points[4].z;
		
		p5.x = lidar_objs_source.obs[i].points[5].x;
		p5.y = lidar_objs_source.obs[i].points[5].y;
		p5.z = lidar_objs_source.obs[i].points[5].z;
		
		p6.x = lidar_objs_source.obs[i].points[6].x;
		p6.y = lidar_objs_source.obs[i].points[6].y;
		p6.z = lidar_objs_source.obs[i].points[6].z;
		
		p7.x = lidar_objs_source.obs[i].points[7].x;
		p7.y = lidar_objs_source.obs[i].points[7].y;
		p7.z = lidar_objs_source.obs[i].points[7].z;
		
		//bottom
		line_list.points.push_back(p0); line_list.points.push_back(p1);
		line_list.points.push_back(p1); line_list.points.push_back(p2);
		line_list.points.push_back(p2); line_list.points.push_back(p3);
		line_list.points.push_back(p3); line_list.points.push_back(p0);
		//top
		line_list.points.push_back(p4); line_list.points.push_back(p5);
		line_list.points.push_back(p5); line_list.points.push_back(p6);
		line_list.points.push_back(p6); line_list.points.push_back(p7);
		line_list.points.push_back(p7); line_list.points.push_back(p4);
		//side
		line_list.points.push_back(p0); line_list.points.push_back(p4);
		line_list.points.push_back(p1); line_list.points.push_back(p5);
		line_list.points.push_back(p2); line_list.points.push_back(p6);
		line_list.points.push_back(p3); line_list.points.push_back(p7);
		
		//direction
		line_list.points.push_back(p0); line_list.points.push_back(p5);
		line_list.points.push_back(p1); line_list.points.push_back(p4);
		MarkerArray.markers.push_back(line_list);
	}
	
	
	pub_lidar_objs_source_show.publish(MarkerArray);//发布融合后的目标框
	//MarkerArray.markers.clear();
	////////////////////////
}

//显示 -20220902
void FusionNode::visualization(const common_msgs::fusionObjects& lidar_objsToTracker){
	////////////////////////
	visualization_msgs::MarkerArray MarkerArray;
	for (size_t i = 0; i < lidar_objsToTracker.obs.size(); i++)
	{
		visualization_msgs::Marker line_list;
		line_list.header.frame_id  = "/velodyne";
		line_list.header.stamp  = ros::Time::now();//TODO  lidar时间戳
		line_list.ns = "points_and_lines";
		line_list.lifetime =ros::Duration(0.1);
		line_list.action = visualization_msgs::Marker::ADD;
		line_list.pose.orientation.w = 1.0;
		line_list.id = i;
		line_list.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list.scale.x = 0.2;
		//line_list.scale.y = 0.1;
		//line_list.scale.z = 0.1;
		//color green
		line_list.color.a = 1;
		line_list.color.r = 0;
		line_list.color.g = 1;
		line_list.color.b = 0;
		
		geometry_msgs::Point p0, p1, p2, p3;
		geometry_msgs::Point p4, p5, p6, p7;
		
		p0.x = lidar_objsToTracker.obs[i].points[0].x;
		p0.y = lidar_objsToTracker.obs[i].points[0].y;
		p0.z = lidar_objsToTracker.obs[i].points[0].z;
		
		p1.x = lidar_objsToTracker.obs[i].points[1].x;
		p1.y = lidar_objsToTracker.obs[i].points[1].y;
		p1.z = lidar_objsToTracker.obs[i].points[1].z;
		
		p2.x = lidar_objsToTracker.obs[i].points[2].x;
		p2.y = lidar_objsToTracker.obs[i].points[2].y;
		p2.z = lidar_objsToTracker.obs[i].points[2].z;
		
		p3.x = lidar_objsToTracker.obs[i].points[3].x;
		p3.y = lidar_objsToTracker.obs[i].points[3].y;
		p3.z = lidar_objsToTracker.obs[i].points[3].z;
		
		p4.x = lidar_objsToTracker.obs[i].points[4].x;
		p4.y = lidar_objsToTracker.obs[i].points[4].y;
		p4.z = lidar_objsToTracker.obs[i].points[4].z;
		
		p5.x = lidar_objsToTracker.obs[i].points[5].x;
		p5.y = lidar_objsToTracker.obs[i].points[5].y;
		p5.z = lidar_objsToTracker.obs[i].points[5].z;
		
		p6.x = lidar_objsToTracker.obs[i].points[6].x;
		p6.y = lidar_objsToTracker.obs[i].points[6].y;
		p6.z = lidar_objsToTracker.obs[i].points[6].z;
		
		p7.x = lidar_objsToTracker.obs[i].points[7].x;
		p7.y = lidar_objsToTracker.obs[i].points[7].y;
		p7.z = lidar_objsToTracker.obs[i].points[7].z;
		
		//bottom
		line_list.points.push_back(p0); line_list.points.push_back(p1);
		line_list.points.push_back(p1); line_list.points.push_back(p2);
		line_list.points.push_back(p2); line_list.points.push_back(p3);
		line_list.points.push_back(p3); line_list.points.push_back(p0);
		//top
		line_list.points.push_back(p4); line_list.points.push_back(p5);
		line_list.points.push_back(p5); line_list.points.push_back(p6);
		line_list.points.push_back(p6); line_list.points.push_back(p7);
		line_list.points.push_back(p7); line_list.points.push_back(p4);
		//side
		line_list.points.push_back(p0); line_list.points.push_back(p4);
		line_list.points.push_back(p1); line_list.points.push_back(p5);
		line_list.points.push_back(p2); line_list.points.push_back(p6);
		line_list.points.push_back(p3); line_list.points.push_back(p7);
		
		//direction
		line_list.points.push_back(p0); line_list.points.push_back(p5);
		line_list.points.push_back(p1); line_list.points.push_back(p4);
		MarkerArray.markers.push_back(line_list);
	}
	
	
	pub_lidar_objs_source_show.publish(MarkerArray);//发布融合后的目标框
	//MarkerArray.markers.clear();
	////////////////////////
}



void FusionNode::SubCallback_lidar_objects(const point_cloud::sensorlidar::ConstPtr &msg_lidar)
{
	pcl::PointCloud<pcl::PointXYZI> lidarObjectCenterPoint;
    common_msgs::sensorobjects1 lidar_objs;
	
	jsk_recognition_msgs::BoundingBoxArray allLidarObjectBox;//所有lidar检测的BOx
	
	int invalidObjectCount = 0;//统计无效检测数量
    lidar_objs.header = msg_lidar->header;
    lidar_objs.timestamp = msg_lidar->header.stamp.toSec();
    for(int i = 0; i < msg_lidar->obs.size(); i++)//lidar检测信息转到sensorobjects1信息
    {
	    //0：car 1:自行车 2：bus 3：tri三轮车 4：行人 5 路锥 6：trunk 7：未知
	    //剔除宽长为0的错误目标 及位置目标
		if(msg_lidar->obs[i].w <= 0 || msg_lidar->obs[i].l <= 0 || msg_lidar->obs[i].classID == 7){
			++invalidObjectCount;
			continue;
		}
		//lidar检测结果格式转换
        common_msgs::sensorobject1 object;
        object.x = msg_lidar->obs[i].x;
        object.value = msg_lidar->obs[i].score;
        object.height = msg_lidar->obs[i].h;
        object.y = msg_lidar->obs[i].y;
        object.length = msg_lidar->obs[i].l;
        object.rt = msg_lidar->obs[i].rt;
        object.z = msg_lidar->obs[i].z;
        object.width = msg_lidar->obs[i].w;
        object.classification = msg_lidar->obs[i].classID;//分类
        lidar_objs.obs.push_back(object);
	
		//目标中心点转成点云发布-lidar坐标系
	    pcl::PointXYZI point_lidar_one;
	    point_lidar_one.x = msg_lidar->obs[i].x;
	    point_lidar_one.y = msg_lidar->obs[i].y;
	    point_lidar_one.z = msg_lidar->obs[i].z;
	    point_lidar_one.intensity = 100;
	    lidarObjectCenterPoint.push_back(point_lidar_one);//lidar检测目标中心点作为点云发布TEMP_pub_all话题  激光点云检测目标的中心点
		
		//发布检测框
		//转成rviz box格式
		jsk_recognition_msgs::BoundingBox oneLidarObjectBox;
		
		oneLidarObjectBox.label = msg_lidar->obs[i].classID;
		oneLidarObjectBox.value = 1;
		
		Eigen::Vector3d eulerAngle(0,0,3.0 / 2.0 * M_PI - msg_lidar->obs[i].rt);//TODO check 航向角旋转
		Eigen::AngleAxisd rollAngle(Eigen::AngleAxisd(eulerAngle(0),Eigen::Vector3d::UnitX()));
		Eigen::AngleAxisd pitchAngle(Eigen::AngleAxisd(eulerAngle(1),Eigen::Vector3d::UnitY()));
		Eigen::AngleAxisd yawAngle(Eigen::AngleAxisd(eulerAngle(2),Eigen::Vector3d::UnitZ()));
		Eigen::Quaterniond quaterniond = yawAngle * pitchAngle * rollAngle;
		
		oneLidarObjectBox.pose.orientation.x = quaterniond.x();
	    oneLidarObjectBox.pose.orientation.y = quaterniond.y();
	    oneLidarObjectBox.pose.orientation.z = quaterniond.z();
	    oneLidarObjectBox.pose.orientation.w = quaterniond.w();
		oneLidarObjectBox.pose.position.x =  msg_lidar->obs[i].x;
	    oneLidarObjectBox.pose.position.y = msg_lidar->obs[i].y;
	    oneLidarObjectBox.pose.position.z = msg_lidar->obs[i].z;
	    oneLidarObjectBox.dimensions.x = msg_lidar->obs[i].w;//TODO wh 是否混淆
	    oneLidarObjectBox.dimensions.y = msg_lidar->obs[i].l;
	    oneLidarObjectBox.dimensions.z = msg_lidar->obs[i].h;
		oneLidarObjectBox.header.frame_id = "velodyne";
		oneLidarObjectBox.header.stamp = msg_lidar->header.stamp;
		allLidarObjectBox.boxes.emplace_back(oneLidarObjectBox);
		
    }
	
	{
		std::mutex lidarObjectMutex;
		std::lock_guard<std::mutex> lockLidarObject(lidarObjectMutex);
		lidar_objects_.push_back(lidar_objs);//存入lidar目标检测结果队列
	}
	
	//std::cout<<"ldiar dection: centerPoint size = "<<lidarObjectCenterPoint.size()<<",bounding box size = "<<allLidarObjectBox.boxes.size()
	//		<<",无效目标个数:"<<invalidObjectCount<<std::endl;
	//publish test cloud 激光点云检测目标的中心点
	sensor_msgs::PointCloud2 lidarObjectCenterPointMsg;
	pcl::toROSMsg(lidarObjectCenterPoint, lidarObjectCenterPointMsg);
	lidarObjectCenterPointMsg.header.frame_id = "velodyne";
	pub_lidar_objs_PCshow.publish(lidarObjectCenterPointMsg);//TEMP_pub_all
	
	//发布点云检测框
	allLidarObjectBox.header.stamp = msg_lidar->header.stamp;
	allLidarObjectBox.header.frame_id = "velodyne";
	pub_lidar_objs_BoxShow_.publish(allLidarObjectBox);
	
	
}


inline void FusionNode::SubCallback_radar_objects(const common_msgs::sensorobjects::ConstPtr &msg)
{
	std::mutex lock_radar;
    std::lock_guard<std::mutex> lock1(lock_radar);
    radar_objects.push_back(*msg);
    //cout<<"\t当前帧radar检测目标数： "<<radar_objects.size()<<endl;
}
void FusionNode::SubCallback_camera_objects(const camera::sensorcamera::ConstPtr &msg)
{
	
    //change type
    common_msgs::sensorobjects *msg_zhuan = new common_msgs::sensorobjects;
    msg_zhuan->timestamp = msg->header.stamp.toSec() * 1000;
    for(int i = 0; i < msg->obj2d.size(); i++)
    {
        common_msgs::sensorobject object;
        object.x = msg->obj2d[i].x;
        object.y = msg->obj2d[i].y;
        object.id = msg->obj2d[i].id;
        object.height = msg->obj2d[i].h;
        object.width = msg->obj2d[i].w;
        object.value = msg->obj2d[i].score;
        object.classification = msg->obj2d[i].classID;
        msg_zhuan->obs.push_back(object);
    }
	{
		std::mutex lock_cam;
		std::lock_guard<std::mutex> lock1(lock_cam);
		camera_objects.push_back(*msg_zhuan);
	}
    //cout<<"\tcamera detection size = "<<camera_objects.size()<<endl;
    delete msg_zhuan;
}

//相机原始数据存储
inline void FusionNode::cameraPictureCallBack(const sensor_msgs::CompressedImage::Ptr cameraPictureMsg){
	std::mutex cameraPictureMutex;
	std::lock_guard<std::mutex> cameraPictureLock(cameraPictureMutex);
	cameraPictureDeque.push_back(*cameraPictureMsg);
	
}

void FusionNode::run(){
	while(ros::ok()){
		fusionAllSensorResult();
		ros::spinOnce();;
	}
}

int main(int argc, char **argv)
{
	ROS_INFO("starting fusion node...");
    ros::init(argc, argv, "fusion");
    ros::NodeHandle n;
    
	FusionNode fusionNode(n);
	fusionNode.run();
	
    return 0;
}
